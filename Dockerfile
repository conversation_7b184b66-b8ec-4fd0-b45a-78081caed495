FROM node as builder

# Create app directory
WORKDIR /usr/src/app

RUN yarn global add typescript

# Install app dependencies
COPY package.json yarn.lock tsconfig.json ./

COPY . .

WORKDIR /usr/src/app/packages/backend

RUN yarn install --frozen-lockfile

RUN yarn build

FROM node:slim

ENV NODE_ENV production

# # Create app directory
WORKDIR /usr/src/app

# # Install app dependencies
COPY ./packages/backend/package.json yarn.lock ./

RUN yarn install --production --frozen-lockfile

COPY --from=builder /usr/src/app/packages/backend/dist ./dist

EXPOSE 8080

ENV PORT 8080

# Update CMD to point to the correct path of app.js
CMD [ "node", "dist/backend/app.js" ]