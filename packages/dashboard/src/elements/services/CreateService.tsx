import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { FormComboBoxPopover } from "@web/components/form/FormComboPopover";
import <PERSON><PERSON>ield from "@web/components/form/FormField";
import S3FileUpload from "@web/components/form/S3FileUpload";
import { FormTextField } from "@web/components/form/FormTextField";
import { Button } from "@web/components/ui/button";
import { Label } from "@web/components/ui/label";
import { ScrollArea } from "@web/components/ui/scroll-area";
import { trpc } from "@web/providers/Providers";
import { createOrUpdateServiceValidator } from "packages/shared/validators/service.validator";
import { FormProvider, UseFormReturn } from "react-hook-form";
import { z } from "zod";
import { toastPromise } from "@web/lib/utils";
import { FormSelect } from "@web/components/form/FormSelect";
import { ServiceStatusEnum } from "packages/shared/types/service";

type Form = z.infer<typeof createOrUpdateServiceValidator>;
type Props = {
  methods: UseFormReturn<Form>;
};

export default function CreateService({ methods }: Props) {
  const { data: categories } = trpc.service.getCategories.useQuery();
  const { data: states } = trpc.lib.states.useQuery();
  const { data: topics } = trpc.manage.getTopics.useQuery({});

  const serviceId = methods.watch("_id");

  trpc.service.getServiceById.useQuery(
    // @ts-expect-error
    serviceId,
    {
      enabled: !!serviceId,
      onSuccess: (data: any) => {
        Object.entries(data).forEach(([key, value]) => {
          // @ts-expect-error
          methods.setValue(key, value);
        });
      },
    }
  );

  const createUpdateService = trpc.service.createUpdateService.useMutation();

  const handleSubmit = methods.handleSubmit((data) =>
    toastPromise({
      asyncFunc: createUpdateService.mutateAsync(data),
      success: "services created successfully",
    })
  );

  return (
    <ScrollArea>
      <form className="m-3" onSubmit={handleSubmit}>
        <FormProvider {...methods}>
          <Label>Create Services</Label>
          <div className="mt-2 flex flex-col gap-4">
            <FormField
              name="title"
              label="Service Name"
              placeholder="Enter service name"
            />
            <FormTextField
              name="description"
              label="Enter Service Description"
              placeholder="Enter service description"
            />
            <FormSelect
              name="status"
              label="Service status"
              placeholder="Select service status"
              options={[
                { label: "Active", value: ServiceStatusEnum.ACTIVE },
                { label: "In-active", value: ServiceStatusEnum.INACTIVE },
              ]}
            />
            <FormComboBoxPopover
              name="category"
              label="Categories"
              placeholder="Select Categories"
              // @ts-ignore
              options={
                categories?.map((category) => ({
                  label: category.title,
                  value: category._id,
                })) || []
              }
            />
            <FormComboBoxPopover
              name="topics"
              label="Topics"
              placeholder="Select Topics"
              // @ts-ignore
              options={
                topics?.map((topic) => ({
                  label: topic.name,
                  value: topic.name,
                })) || []
              }
            />
            <FormField
              name="email"
              label="Email"
              placeholder="Enter service email"
              type="email"
            />
            <FormField
              name="organization"
              label="Organization"
              placeholder="Enter service organization"
            />
            <FormField
              name="phone"
              label="Contact"
              placeholder="Enter service contact"
              type="tel"
            />
            <FormComboBoxPopover
              name="states"
              label="State"
              placeholder="Select State"
              // @ts-ignore
              options={[
                { label: "National", value: "National" },
                ...(states?.map((state) => ({
                  label: state,
                  value: state,
                })) || []),
              ]}
            />
            <FormField
              name="website"
              label="Service Website"
              placeholder="Enter service website"
            />
            <S3FileUpload name="logo" path="services" prefix="service" />
            <Button>{serviceId ? "Update Service" : "Create Service"}</Button>
          </div>
        </FormProvider>
      </form>
    </ScrollArea>
  );
}
