//@ts-nocheck

import { Label } from "@web/components/ui/label";
import { ScrollArea } from "@web/components/ui/scroll-area";
import { RouterOutput } from "../../../../shared";
import React, { useEffect, useMemo, useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { cn } from "@web/lib/utils";
import { Badge } from "@web/components/ui/badge";

type Node = { key: string; value: string; children?: Node[] };

function getAllChildTags(input = "Bone Health", nodes: Node[]) {
  const values: string[] = [];
  let valueHit = false;
  let branchHit = false;

  for (let i in nodes) {
    if (branchHit) break;

    traverseNode(nodes[i]);
  }

  function traverseNode(node: Node) {
    if (!node.children?.length) return;

    // Parent node
    if (node.value === input) {
      {
        valueHit = true;
        branchHit = true;
      }
      values.push(node.value);
    }

    // Child Nodes
    node.children.forEach((child) => {
      if (input === child.value) {
        valueHit = true;
        branchHit = true;
      }
      if (valueHit) values.push(child.value);

      traverseNode(child);
    });
  }

  return values;
}

function getChildNode(input = "Bone Health", nodes: Node[]) {
  for (let i in nodes) {
    const node = traverseNode(nodes[i]);
    if (node) return node;
  }

  function traverseNode(node: Node) {
    if (!node.children?.length) return;

    // Parent node
    if (node.value === input) return node;

    for (let child of node.children) {
      if (child.value === input) return child;
      traverseNode(child);
    }
  }
}

type SelectedDisease =
  keyof RouterOutput["lib"]["healthProfileOptions"]["diseaseTypes"];

// Disease > Diagnosis > Cancer Journey Stage
export default function BlogDiseaseTags({
  healthProfileOptions,
}: {
  healthProfileOptions?: RouterOutput["lib"]["healthProfileOptions"];
}) {
  if (!healthProfileOptions) return null;

  const methods = useFormContext();
  const { diseaseTypes, diagnosisTypes } = healthProfileOptions;
  const [secondLevelTags, setSecondLevelTags] = useState<string[]>([]);
  const [thirdLevelTags, setThirdLevelTags] = useState<string[]>([]);

  const selectedTags: string[] = methods.watch("diseaseTags") ?? [];

  useEffect(() => {
    // first level tags
    const firstLevelTags = Object.values(diseaseTypes).map((_) => _);

    // second level tags
    const allowedFirstLevelTags = selectedTags.filter((st) =>
      firstLevelTags.includes(st)
    );
  }, [selectedTags, diseaseTypes]);

  const diseaseTree: Node[] = [];

  Object.entries(diseaseTypes).forEach(([diseaseKey, diseaseValue]) => {
    const diagnosis = diagnosisTypes[diseaseKey];

    const diseaseChildren: Node[] = diagnosis.map((d) => {
      return {
        key: d.name,
        value: d.name,
        children: d.subTypes.map((s) => {
          return {
            key: s,
            value: s,
          };
        }),
      };
    });

    diseaseTree.push({
      key: diseaseKey,
      value: diseaseValue,
      children: diseaseChildren,
    });
  });

  const result = getAllChildTags("Bladder Cancer", diseaseTree);

  const onTagSelect = (tag: string, selected: "first" | "second" | "third") => {
    const tagExists = selectedTags.includes(tag);
    if (tagExists) {
      // get tags to remove
      const tagsToRemove = getAllChildTags(tag, diseaseTree);
      const res = selectedTags.filter(
        (selectedTag) => !tagsToRemove.includes(selectedTag)
      );

      methods.setValue("diseaseTags", res);

      if (selected === "first") {
        setSecondLevelTags((t) => t.filter((_) => !tagsToRemove.includes(_)));
        setThirdLevelTags((t) => t.filter((_) => !tagsToRemove.includes(_)));
      }

      if (selected === "second") {
        setThirdLevelTags((t) => t.filter((_) => !tagsToRemove.includes(_)));
      }

      return;
    }

    methods.setValue("diseaseTags", [...selectedTags, tag]);

    if (selected === "first") {
      setSecondLevelTags((_) => [
        ..._,
        ...(getChildNode(tag, diseaseTree)?.children || [])
          .map((child) => child.value)
          .flat(),
      ]);
    }

    if (selected === "second") {
      setThirdLevelTags((_) => [
        ..._,
        ...(getChildNode(tag, diseaseTree)?.children || [])
          .map((child) => child.value)
          .flat(),
      ]);
    }
  };

  return (
    <ScrollArea className="h-[800px] text-xs">
      <Label>Select Diagnosis</Label>
      <div className="divide-y ">
        <Label className="underline">Disease</Label>

        {Object.values(diseaseTypes).map((item) => {
          const itemExists = selectedTags.includes(item);

          return (
            <div
              key={item}
              className={cn(
                "cursor-pointer py-2",
                itemExists && "text-primary font-bold"
              )}
              onClick={() => onTagSelect(item, "first")}
            >
              {item}
            </div>
          );
        })}
      </div>
      <div className="divide-y">
        <Label className="underline">Diagnosis</Label>

        {secondLevelTags.map((item) => {
          const itemExists = selectedTags.includes(item);

          return (
            <div
              key={item}
              className={cn(
                "cursor-pointer py-2",
                itemExists && "text-primary font-bold"
              )}
              onClick={() => onTagSelect(item, "second")}
            >
              {item}
            </div>
          );
        })}
      </div>
      <div className="divide-y">
        <Label className="underline">Cancer Stage</Label>

        {thirdLevelTags.map((item) => {
          const itemExists = selectedTags.includes(item);

          return (
            <div
              key={item}
              className={cn(
                "cursor-pointer py-2",
                itemExists && "text-primary font-bold"
              )}
              onClick={() => onTagSelect(item, "third")}
            >
              {item}
            </div>
          );
        })}
      </div>
    </ScrollArea>
  );
}
