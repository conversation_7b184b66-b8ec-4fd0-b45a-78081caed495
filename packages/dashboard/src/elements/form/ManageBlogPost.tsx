import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  UseFormReturn,
} from "react-hook-form";
import { manageBlogValidator } from "../../../../shared/validators/blog";
import { z } from "zod";
import FormField from "@web/components/form/FormField";
import { Label } from "@radix-ui/react-label";
import { BlockNoteView, useCreateBlockNote } from "@blocknote/react";
import { FormComboBoxPopover } from "@web/components/form/FormComboPopover";
import { RouterOutput } from "../../../../shared";
import S3FileUpload from "@web/components/form/S3FileUpload";
import { FormSelect } from "@web/components/form/FormSelect";
import { BlogStatus } from "../../../../shared/types/Blog";
import { FormDatePicker } from "@web/components/form/FormDatePicker";
import { Button } from "@web/components/ui/button";
import { uploadToS3 } from "@web/lib/utils";
import { trpc } from "@web/providers/Providers";
import { FormTextField } from "@web/components/form/FormTextField";
import Image from "next/image";
import { useMemo } from "react";
import _ from "lodash";

type Form = z.infer<typeof manageBlogValidator>;

export default function ManageBlogPostForm({
  methods,
  blogTags,
  onFormSubmit,
}: {
  methods: UseFormReturn<Form>;
  blogTags: RouterOutput["blog"]["getBlogTags"];
  onFormSubmit: SubmitHandler<Form>;
}) {
  const s3SignedUrl = trpc.lib.servicesS3SignedUrl.useMutation();

  const manageTopics = trpc.manage.getTopics.useQuery({});

  // TODO
  const manageTopicTags = useMemo(() => {
    let tags: { label: string; value: string }[] = [];
    manageTopics.data?.forEach((topic) => {
      topic.tags.forEach((tag) => {
        tags.push({ label: tag, value: tag });
      });
    });

    return tags;
  }, [manageTopics.data]);

  const allTags = useMemo(() => {
    const tags = [
      ...blogTags.map((tag) => ({ label: tag.tag, value: tag.tag })),
      ...manageTopicTags,
    ];

    return _.uniqBy(tags, "label");
  }, [blogTags, manageTopicTags]);

  console.log({ allTags });

  const uploadFile = async (file: File) => {
    const { signedUrl, s3FileUrl } = await s3SignedUrl.mutateAsync({
      ext: "png",
      path: "blogs",
      prefix: "blogs",
    });

    await uploadToS3({ signedUrl, file: file });

    return s3FileUrl;
  };

  const { data: healthProfileOptions } =
    trpc.lib.healthProfileOptions.useQuery();

  const editor = useCreateBlockNote({
    uploadFile,
    ...(methods.getValues("editorData")?.length && {
      initialContent: JSON.parse(methods.getValues("editorData")),
    }),
  });

  const bannerImage = methods.watch("banner");
  return (
    <form className="space-y-4 p-1">
      <FormProvider {...methods}>
        <FormTextField
          name="title"
          label="Blog Title"
          placeholder="Enter title"
        />
        <div>
          <Label>Content</Label>
          <Controller
            name="contentHtml"
            render={({ field: { onChange, value } }) => {
              const handleChange = async () => {
                // Converts the editor's contents from Block objects to Markdown and store to state.
                const markdown = await editor.blocksToMarkdownLossy(
                  editor.document
                );
                // const markdown = await editor.blocksToHTMLLossy(editor.document);
                methods.setValue("contentHtml", markdown);
                methods.setValue("editorData", JSON.stringify(editor.document));
              };

              return (
                <BlockNoteView
                  editor={editor}
                  className="w-full border-2"
                  theme="light"
                  onChange={handleChange}
                  content={value}
                />
              );
            }}
          />
        </div>

        {/* TODO: blog tags +  manage tags => done*/}
        <FormSelect name="tags" options={allTags} />

        <FormField name="author" label="Author" placeholder="Enter author" />
        <FormTextField
          name="description"
          label="Description"
          placeholder="Enter description"
        />
        <FormField
          name="timeToReadInMins"
          label="Read time in minutes "
          placeholder="Enter time to read in minutes "
          type="number"
        />
        <FormComboBoxPopover
          name="tags"
          label="Tags"
          placeholder="Select tags"
          options={(blogTags || []).map((t) => ({
            label: t.tag,
            value: String(t._id),
          }))}
        />
        <Label>Banner</Label>
        {bannerImage && (
          <Image
            key={bannerImage}
            width={300}
            height={150}
            src={bannerImage}
            alt="banner"
            className="object-contain"
          />
        )}
        <S3FileUpload name="banner" path="blogs" prefix="blog" />
        <FormSelect
          name="status"
          label="Status"
          placeholder="Set status"
          options={[
            { label: "Draft", value: BlogStatus.DRAFT },
            { label: "Publish", value: BlogStatus.PUBLISHED },
          ]}
        />
        <FormDatePicker
          name="publishDate"
          label="Publish Date"
          placeholder="Select publish date"
        />
      </FormProvider>
      <Button onClick={methods.handleSubmit(onFormSubmit)}>Submit</Button>
    </form>
  );
}
