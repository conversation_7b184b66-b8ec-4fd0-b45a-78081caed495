"use client";

import { But<PERSON> } from "@web/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@web/components/ui/table";
import { toastPromise } from "@web/lib/utils";
import { trpc } from "@web/providers/Providers";

export default function Tags() {
  const tags = trpc.blog.getBlogTags.useQuery();
  const deleteTag = trpc.blog.deleteBlogTag.useMutation();

  const handleteDeleteTag = async (tagId: string) => {
    toastPromise({
      asyncFunc: deleteTag.mutateAsync(tagId),
      success: "tag deleted successfully",
      onSuccess() {
        tags.refetch();
      },
    });
  };

  if (tags.isLoading) return null;
  return (
    <div className="h-[calc(100vh-105px)] gap-5 mt-7">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Sno</TableHead>
            <TableHead>Tag</TableHead>
            <TableHead>Action</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {tags.data?.map((t, idx) => (
            <TableRow>
              <TableCell>{idx}</TableCell>
              <TableCell>{t.tag}</TableCell>
              <TableCell>
                <Button
                  onClick={() => handleteDeleteTag(String(t._id))}
                  variant="destructive"
                >
                  Delete
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
