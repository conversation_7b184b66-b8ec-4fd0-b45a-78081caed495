"use client";
import { Badge } from "@web/components/ui/badge";
import { <PERSON><PERSON> } from "@web/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@web/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@web/components/ui/table";
import ManageBlogPostForm from "@web/elements/form/ManageBlogPost";
import { trpc } from "@web/providers/Providers";
import { format } from "date-fns";
import { Edit, Trash } from "lucide-react";
import Link from "next/link";
import { manageBlogValidator } from "../../../../shared/validators/blog";
import { z } from "zod";
import { SubmitHandler, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { BlogStatus } from "../../../../shared/types/Blog";
import { useState } from "react";
import { RouterOutput } from "../../../../shared";
import { toastPromise } from "@web/lib/utils";
import { useRouter } from "next/navigation";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@web/components/ui/alert-dialog";

type BlogForm = z.infer<typeof manageBlogValidator>;

export default function UpdateBlog() {
  const [open, setOpen] = useState(false);

  const blogs = trpc.blog.getBlogs.useQuery();
  const blogTags = trpc.blog.getBlogTags.useQuery();

  const methods = useForm<BlogForm>({
    resolver: zodResolver(manageBlogValidator),
    defaultValues: {
      author: "",
      banner: "",
      blogId: "",
      contentHtml: "",
      description: "",
      status: BlogStatus.DRAFT,
      tags: [],
      timeToReadInMins: "" as unknown as number,
      title: "",
      editorData: [],
    },
  });

  const createBlog = trpc.blog.createBlog.useMutation();
  const deleteBlog = trpc.blog.deleteBlog.useMutation();
  const router = useRouter();

  const handleSubmit: SubmitHandler<BlogForm> = (data) =>
    toastPromise({
      asyncFunc: createBlog.mutateAsync({
        ...data,
        diseaseTags: (data.diseaseTags || []).filter(Boolean),
      }),
      success: "Blog Updated Succesfully ",
      onSuccess: () => {
        methods.reset();
        router.replace("/learn");
        blogs.refetch();
        setOpen(false);
      },
    });

  const onBlogSelect = (blog: RouterOutput["blog"]["getBlogs"][number]) => {
    methods.setValue("title", blog.title);
    methods.setValue("author", blog.author);
    methods.setValue("banner", blog.banner as string);
    methods.setValue("blogId", String(blog._id));
    methods.setValue("contentHtml", blog.contentHtml);
    methods.setValue("description", blog.description);
    methods.setValue("publishDate", blog.publishDate);
    methods.setValue("diseaseTags", blog.diseaseTags);
    methods.setValue("status", blog.status);
    methods.setValue("editorData", blog.editorData);
    methods.setValue(
      "tags",
      (blog.tags || []).map((_) => String(_._id))
    );
    methods.setValue("timeToReadInMins", blog.timeToReadInMins as number);

    setOpen(true);
  };

  return (
    <div className="container flex flex-col  ">
      <div className="flex  justify-end mt-5 gap-4">
        <Button>
          <Link href={"/blogs/create"}>Create Blog</Link>
        </Button>
        <Button>
          <Link href={"/blogs/manage"}>Dashboard</Link>
        </Button>
      </div>

      {/* Update Blogpost dialog */}
      <AlertDialog open={open} onOpenChange={setOpen}>
        <AlertDialogContent className="min-w-[80vw]">
          <AlertDialogHeader>
            <AlertDialogTitle>Update Blog Post</AlertDialogTitle>
          </AlertDialogHeader>
          <div className="max-h-[70vh] overflow-y-scroll">
            <ManageBlogPostForm
              methods={methods}
              blogTags={blogTags.data || []}
              onFormSubmit={handleSubmit}
            />
          </div>
        </AlertDialogContent>
      </AlertDialog>

      <Table className="mt-20">
        <TableHeader>
          <TableRow>
            <TableHead>Blog Title</TableHead>
            <TableHead>Author</TableHead>
            <TableHead>Diagnosis</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Publish Date</TableHead>
            <TableHead>Action</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {blogs.data?.map((b) => {
            return (
              <TableRow key={String(b._id)}>
                <TableCell>{b.title}</TableCell>
                <TableCell>{b.author}</TableCell>
                <TableCell className="space-x-2">
                  {b.tags?.map((t) => {
                    return <Badge key={String(t._id)}>{t.tag}</Badge>;
                  })}
                </TableCell>
                <TableCell>{b.status}</TableCell>
                <TableCell>
                  {b.publishDate && format(b.publishDate, "PPP")}
                </TableCell>
                <TableCell className="flex items-center gap-2">
                  <Button onClick={() => onBlogSelect(b)}>
                    <Edit />
                    Update
                  </Button>
                  <Button
                    size="icon"
                    onClick={() =>
                      toastPromise({
                        asyncFunc: deleteBlog.mutateAsync(String(b._id)),
                        success: "blog deleted successfully",
                        onSuccess() {
                          blogs.refetch();
                        },
                      })
                    }
                  >
                    <Trash />
                  </Button>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
