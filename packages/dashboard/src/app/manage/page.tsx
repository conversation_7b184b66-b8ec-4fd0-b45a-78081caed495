"use client";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormComboBoxPopover } from "@web/components/form/FormComboPopover";
import FormField from "@web/components/form/FormField";
import { FormSelect } from "@web/components/form/FormSelect";
import { Button } from "@web/components/ui/button";
import { Label } from "@web/components/ui/label";
import { FormProvider, useFieldArray, useForm } from "react-hook-form";
import { z } from "zod";

const questionsSchema = z.object({
  question: z.string().min(1, "question is required"),
  options: z.array(z.string().min(1, "option is required")),
});

const schema = z.object({
  name: z.string().min(1, "Topic name is required"),
  tags: z.array(z.string()),
  questions: z.array(questionsSchema),
  dailyTrackerQuestions: z.array(questionsSchema),
});

type Form = z.infer<typeof schema>;

export default function Manage() {
  const methods = useForm<Form>({
    defaultValues: {
      name: "",
      tags: [],
      questions: [{ question: "", options: [] }],
      dailyTrackerQuestions: [{ question: "", options: [] }],
    },
    resolver: zodResolver(schema),
  });

  const questionsFieldArray = useFieldArray({
    control: methods.control,
    name: "questions",
  });

  const dailyTrackerQuestionsFieldArray = useFieldArray({
    control: methods.control,
    name: "dailyTrackerQuestions",
  });

  return (
    <div className="min-h-screen container">
      <h2 className="font-semibold mb-4 underline">Create Topic</h2>
      <form className="max-w-3xl">
        <FormProvider {...methods}>
          <div>
            <FormField
              name="name"
              label="Topic Name"
              placeholder="Enter topic name"
            />
            <FormComboBoxPopover
              name="tags"
              label="Topic Tags"
              placeholder="Select tags"
              options={[
                { label: "Tag 1", value: "tag1" },
                { label: "Tag 2", value: "tag2" },
              ]}
            />
            <div className="border border-primary mt-4 p-3 space-y-4 rounded-lg">
              <div className="flex items-center justify-between">
                <Label>Subscription Questions</Label>
                <div className="flex items-center gap-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() =>
                      questionsFieldArray.append({ options: [], question: "" })
                    }
                  >
                    Add
                  </Button>
                  <Button
                    variant="destructive"
                    type="button"
                    onClick={() =>
                      questionsFieldArray.remove(
                        questionsFieldArray.fields.length - 1
                      )
                    }
                  >
                    Remove
                  </Button>
                </div>
              </div>

              <div className="divide-y divide-dashed">
                {questionsFieldArray.fields.map((q) => {
                  return (
                    <div key={q.id} className="mt-4">
                      <FormField
                        name={`questions.${q.id}.question`}
                        label="Question"
                        placeholder="Enter Question"
                      />
                      <FormComboBoxPopover
                        name={`questions.${q.id}.options`}
                        label="Question"
                        options={[
                          { label: "Option 1", value: "1" },
                          { label: "Option 2", value: "2" },
                        ]}
                      />
                    </div>
                  );
                })}
              </div>
            </div>
            <div className="border border-primary mt-4 p-3 space-y-4 rounded-lg">
              <div className="flex items-center justify-between">
                <Label>Daily Tracker Questions</Label>
                <div className="flex items-center gap-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() =>
                      dailyTrackerQuestionsFieldArray.append({
                        options: [],
                        question: "",
                      })
                    }
                  >
                    Add
                  </Button>
                  <Button
                    type="button"
                    variant="destructive"
                    onClick={() =>
                      dailyTrackerQuestionsFieldArray.remove(
                        dailyTrackerQuestionsFieldArray.fields.length - 1
                      )
                    }
                  >
                    Remove
                  </Button>
                </div>
              </div>
              <div className="divide-y divide-dashed">
                {dailyTrackerQuestionsFieldArray.fields.map((q) => {
                  return (
                    <div key={q.id} className="mt-4">
                      <FormField
                        name={`questions.${q.id}.question`}
                        label="Question"
                        placeholder="Enter Question"
                      />
                      <FormSelect
                        name="type"
                        label="Question Input Type"
                        options={[
                          { label: "Text", value: "text" },
                          { label: "Number", value: "number" },
                          { label: "Date", value: "date" },
                        ]}
                      />
                      <FormComboBoxPopover
                        name={`questions.${q.id}.options`}
                        label="Question"
                        options={[
                          { label: "Option 1", value: "1" },
                          { label: "Option 2", value: "2" },
                        ]}
                      />
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </FormProvider>
      </form>
    </div>
  );
}
