"use client";
import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import FormChipInput from "@web/components/form/FormChipInput";
import { FormComboBoxPopover } from "@web/components/form/FormComboPopover";
import FormField from "@web/components/form/FormField";
import { FormSelect } from "@web/components/form/FormSelect";
import { FormTextField } from "@web/components/form/FormTextField";
import { Button } from "@web/components/ui/button";
import { Card, CardContent, CardHeader } from "@web/components/ui/card";
import { Checkbox } from "@web/components/ui/checkbox";
import { Label } from "@web/components/ui/label";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@web/components/ui/tabs";
import { GripVertical, Plus, Trash } from "lucide-react";
import {
  ActionTypes,
  EventTypes,
  QuestionTypes,
} from "packages/shared/types/manage";
import { useState } from "react";
import {
  Form<PERSON>rov<PERSON>,
  useFieldArray,
  useForm,
  useForm<PERSON>ontext,
} from "react-hook-form";
import { z } from "zod";

// Action schema
const actionSchema = z.object({
  actionType: z.enum([ActionTypes.NextQuestion, ActionTypes.Call]),
  event: z.enum([EventTypes.OptionSelect]),
  metadata: z.record(z.any()),
});

// Option schema
const optionSchema = z.object({
  _id: z.string(),
  value: z.string(),
  prompt: z.string().optional(),
  externalId: z.string(),
  color: z.string().optional(),
  description: z.union([z.string(), z.array(z.any())]).optional(),
  endOfQuestions: z.boolean().optional().default(false),
  carePartnerValue: z.string().optional(),
});

const regularQuestionSchema = z.object({
  question: z.string(),
  externalId: z.string(),
  type: z.nativeEnum(QuestionTypes),
  options: z.array(
    z.object({
      value: z.string(),
      externalId: z.string(),
      prompt: z.string(),
    })
  ),
});
const learnQuestionSchema = z.object({
  question: z.string(),
  externalId: z.string(),
  type: z.nativeEnum(QuestionTypes),
  options: z.array(
    z.object({
      value: z.string(),
      description: z.string(),
      color: z.string(),
      externalId: z.string(),
    })
  ),
});

// Question schema
const questionSchema = z.object({
  question: z.string(),
  subText: z.string().optional(),
  type: z.nativeEnum(QuestionTypes).optional().default(QuestionTypes.Select),
  externalId: z.string(),
  isOptional: z.boolean().optional().default(false),
  isSchedule: z.boolean().optional().default(false),
  duration: z.number().optional(),
  delay: z.number().optional(),
  recurringQuestion: z.boolean().optional().default(false),
  carePartnerText: z.string().optional(),
  getNextCompletedQuestion: z.string().optional(),
  actions: z.array(actionSchema).optional().default([]),
  options: z.array(optionSchema),
});

// Topic schema
export const topicSchema = z.object({
  _id: z.string().optional(),
  name: z.string(),
  nickname: z.string(),
  category: z.string(),
  externalId: z.string(),
  tags: z.array(z.string()),
  questions: z.array(regularQuestionSchema),
  learningQuestions: z.array(learnQuestionSchema).optional().default([]),
  dailyTrackerQuestions: z.array(questionSchema).optional().default([]),
});

type Form = z.infer<typeof topicSchema>;

export default function ManageCreate() {
  const methods = useForm<Form>({
    resolver: zodResolver(topicSchema),
    defaultValues: {
      name: "",
      nickname: "",
      category: "",
      externalId: crypto.randomUUID(),
      tags: [],
      questions: [],
      dailyTrackerQuestions: [],
      learningQuestions: [],
    },
  });

  // Subscription questions field array
  const {
    fields: questions,
    append: appendQuestion,
    remove: removeQuestion,
    move: moveQuestion,
  } = useFieldArray({
    name: "questions",
    control: methods.control,
  });

  // Daily tracker questions field array
  const {
    fields: dailyTrackerQuestions,
    append: appendDailyTrackerQuestion,
    remove: removeDailyTrackerQuestion,
    move: moveDailyTrackerQuestion,
  } = useFieldArray({
    control: methods.control,
    name: "dailyTrackerQuestions",
  });

  // Learning questions field array
  const {
    fields: learningQuestions,
    append: appendLearningQuestion,
    remove: removeLearningQuestion,
    move: moveLearningQuestion,
  } = useFieldArray({
    control: methods.control,
    name: "learningQuestions",
  });

  // // Handle drag and drop for Subscription questions
  // const onDragEnd = (result) => {
  //   if (!result.destination) return;
  //   moveQuestion(result.source.index, result.destination.index);
  // };

  // // Handle drag and drop for daily tracker questions
  // const onDailyTrackerDragEnd = (result) => {
  //   if (!result.destination) return;
  //   moveDailyTrackerQuestion(result.source.index, result.destination.index);
  // };

  // // Handle drag and drop for learning questions
  // const onLearningDragEnd = (result) => {
  //   if (!result.destination) return;
  //   moveLearningQuestion(result.source.index, result.destination.index);
  // };

  return (
    <div className="container py-6">
      <h1 className="text-2xl font-bold mb-6">Create Topic</h1>

      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit((data) => console.log(data))}>
          <Tabs defaultValue="basic">
            <TabsList className="mb-4">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="questions">
                Subscription Questions
              </TabsTrigger>
              <TabsTrigger value="dailyTracker">
                Daily Tracker Questions
              </TabsTrigger>
              <TabsTrigger value="learning">Learning Questions</TabsTrigger>
            </TabsList>

            {/* Basic Info Tab */}
            <TabsContent value="basic" className="space-y-4 py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  name="name"
                  label="Topic Name"
                  placeholder="Enter topic name"
                />
                <FormField
                  name="nickname"
                  label="Nickname"
                  placeholder="Enter nickname"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  name="category"
                  label="Category"
                  placeholder="Enter category"
                />
                <FormField
                  name="externalId"
                  label="External ID"
                  placeholder="Enter external ID"
                  disabled
                />
              </div>

              <div>
                <FormChipInput
                  name="tags"
                  label="Tags"
                  placeholder="Select or create tags"
                />
              </div>
            </TabsContent>

            {/* Subscription Questions Tab */}
            <TabsContent value="questions" className="py-4">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">
                  Subscription Questions
                </h2>
                <Button
                  type="button"
                  onClick={() =>
                    appendQuestion({
                      question: "",
                      type: QuestionTypes.Select,
                      options: [],
                      externalId: crypto.randomUUID(),
                    })
                  }
                >
                  <Plus className="mr-2 h-4 w-4" /> Add Question
                </Button>
              </div>

              {questions.length === 0 ? (
                <div className="text-center p-8 border border-dashed rounded-lg">
                  <p className="text-muted-foreground">
                    No questions added yet. Click "Add Question" to get started.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {questions.map((field, index) => (
                    <Card key={field.id} className="border-primary">
                      <CardHeader className="flex flex-row items-center justify-between p-4 pb-2">
                        <div className="flex items-center">
                          <GripVertical className="h-5 w-5 text-muted-foreground mr-2 cursor-grab" />
                          <h3 className="font-medium">Question {index + 1}</h3>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeQuestion(index)}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </CardHeader>
                      <CardContent className="p-4 pt-0 space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            name={`questions.${index}.question`}
                            label="Question Text"
                            placeholder="Enter question"
                          />
                          <FormField
                            name={`questions.${index}.externalId`}
                            label="External ID"
                            placeholder="Enter external ID"
                            disabled
                          />
                          <FormSelect
                            name={`questions.${index}.type`}
                            disabled
                            label="Question Type (Only Select is supported)"
                            options={Object.values(QuestionTypes).map(
                              (type) => ({
                                label: type,
                                value: type,
                              })
                            )}
                          />
                        </div>

                        <RegularQuestionOptions questionIndex={index} />
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            {/* Learning Questions Tab */}
            <TabsContent value="learning" className="py-4">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Learning Questions</h2>
                <Button
                  type="button"
                  onClick={() =>
                    appendLearningQuestion({
                      question: "",
                      externalId: crypto.randomUUID(),
                      type: QuestionTypes.Select,
                      options: [],
                    })
                  }
                >
                  <Plus className="mr-2 h-4 w-4" /> Add Learning Question
                </Button>
              </div>

              {learningQuestions.length === 0 ? (
                <div className="text-center p-8 border border-dashed rounded-lg">
                  <p className="text-muted-foreground">
                    No learning questions added yet. Click "Add Learning
                    Question" to get started.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {learningQuestions.map((field, index) => (
                    <Card key={field.id} className="border-primary">
                      <CardHeader className="flex flex-row items-center justify-between p-4 pb-2">
                        <div className="flex items-center">
                          <GripVertical className="h-5 w-5 text-muted-foreground mr-2 cursor-grab" />
                          <h3 className="font-medium">
                            Learning Question {index + 1}
                          </h3>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeLearningQuestion(index)}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </CardHeader>
                      <CardContent className="p-4 pt-0 space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            name={`learningQuestions.${index}.question`}
                            label="Question Text"
                            placeholder="Enter question"
                          />
                          <FormField
                            name={`learningQuestions.${index}.externalId`}
                            label="External ID"
                            placeholder="Enter external ID"
                            disabled
                          />
                          <FormSelect
                            name={`learningQuestions.${index}.type`}
                            disabled
                            label="Question Type (Only Select is supported)"
                            options={Object.values(QuestionTypes).map(
                              (type) => ({
                                label: type,
                                value: type,
                              })
                            )}
                          />
                        </div>

                        <LearnQuestionOptions
                          questionIndex={index}
                          questionType="learningQuestions"
                        />
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            {/* Daily Tracker Questions Tab */}
            <TabsContent value="dailyTracker" className="py-4">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">
                  Daily Tracker Questions
                </h2>
                <Button
                  type="button"
                  onClick={() =>
                    appendDailyTrackerQuestion({
                      question: "",
                      type: "Select",
                      isOptional: false,
                      options: [],
                    })
                  }
                >
                  <Plus className="mr-2 h-4 w-4" /> Add Daily Tracker Question
                </Button>
              </div>

              {dailyTrackerQuestions.length === 0 ? (
                <div className="text-center p-8 border border-dashed rounded-lg">
                  <p className="text-muted-foreground">
                    No daily tracker questions added yet. Click "Add Daily
                    Tracker Question" to get started.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {dailyTrackerQuestions.map((field, index) => (
                    <Card key={field.id} className="border-primary">
                      <CardHeader className="flex flex-row items-center justify-between p-4 pb-2">
                        <div className="flex items-center">
                          <GripVertical className="h-5 w-5 text-muted-foreground mr-2 cursor-grab" />
                          <h3 className="font-medium">
                            Daily Tracker Question {index + 1}
                          </h3>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeDailyTrackerQuestion(index)}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </CardHeader>
                      <CardContent className="p-4 pt-0 space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            name={`dailyTrackerQuestions.${index}.question`}
                            label="Question Text"
                            placeholder="Enter question"
                          />
                          <FormField
                            name={`dailyTrackerQuestions.${index}.externalId`}
                            label="External ID"
                            placeholder="Enter external ID"
                          />
                        </div>

                        <FormField
                          name={`dailyTrackerQuestions.${index}.subText`}
                          label="Sub Text (Optional)"
                          placeholder="Enter sub text"
                        />

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormSelect
                            name={`dailyTrackerQuestions.${index}.type`}
                            label="Question Type"
                            options={[
                              { label: "Select", value: "Select" },
                              { label: "Multi-Select", value: "MultiSelect" },
                              { label: "Yes/No", value: "Yes/No" },
                              {
                                label: "Color Question",
                                value: "Color question",
                              },
                              {
                                label: "Horizontal Select",
                                value: "HorizontalSelect",
                              },
                              { label: "Emergency", value: "Emergency" },
                              { label: "Completed", value: "Completed" },
                            ]}
                          />

                          <FormField
                            name={`dailyTrackerQuestions.${index}.carePartnerText`}
                            label="Care Partner Text (Optional)"
                            placeholder="Enter care partner text"
                          />
                        </div>

                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id={`dailyTrackerQuestions.${index}.isOptional`}
                              {...methods.register(
                                `dailyTrackerQuestions.${index}.isOptional` as any
                              )}
                            />
                            <Label
                              htmlFor={`dailyTrackerQuestions.${index}.isOptional`}
                            >
                              Optional Question
                            </Label>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id={`dailyTrackerQuestions.${index}.isSchedule`}
                              {...methods.register(
                                `dailyTrackerQuestions.${index}.isSchedule` as any
                              )}
                            />
                            <Label
                              htmlFor={`dailyTrackerQuestions.${index}.isSchedule`}
                            >
                              Scheduled Question
                            </Label>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id={`dailyTrackerQuestions.${index}.recurringQuestion`}
                              {...methods.register(
                                `dailyTrackerQuestions.${index}.recurringQuestion` as any
                              )}
                            />
                            <Label
                              htmlFor={`dailyTrackerQuestions.${index}.recurringQuestion`}
                            >
                              Recurring Question
                            </Label>
                          </div>
                        </div>

                        {methods.watch(
                          `dailyTrackerQuestions.${index}.isSchedule` as any
                        ) && (
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <FormField
                              name={`dailyTrackerQuestions.${index}.delay`}
                              label="Delay (days)"
                              type="number"
                              placeholder="Enter delay in days"
                            />
                            <FormField
                              name={`dailyTrackerQuestions.${index}.duration`}
                              label="Duration (days)"
                              type="number"
                              placeholder="Enter duration in days"
                            />
                          </div>
                        )}

                        <QuestionOptions
                          questionIndex={index}
                          questionType="dailyTrackerQuestions"
                        />
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>

          <div className="mt-6 flex justify-end">
            <Button type="submit" className="px-8">
              Save Topic
            </Button>
          </div>
        </form>
      </FormProvider>
    </div>
  );
}

// Define types for our components
interface QuestionOptionsProps {
  questionIndex: number;
  questionType?: string;
}

interface OptionActionsProps {
  questionIndex: number;
  optionIndex: number;
  questionType?: string;
}

interface ActionMetadataProps {
  questionIndex: number;
  optionIndex: number;
  actionIndex: number;
  questionType?: string;
}

function QuestionOptions({
  questionIndex,
  questionType = "questions",
}: QuestionOptionsProps) {
  const { control } = useFormContext();

  const {
    fields: options,
    append: appendOption,
    remove: removeOption,
  } = useFieldArray({
    control,
    name: `${questionType}.${questionIndex}.options` as any,
  });

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h4 className="font-medium">Options</h4>
        <Button
          type="button"
          size="sm"
          onClick={() =>
            appendOption({
              value: "",
              color: "",
              externalId: crypto.randomUUID(),
              actions: [],
            })
          }
        >
          <Plus className="mr-2 h-3 w-3" /> Add Option
        </Button>
      </div>

      {options.length === 0 ? (
        <div className="text-center p-4 border border-dashed rounded-lg">
          <p className="text-muted-foreground text-sm">
            No options added yet. Click "Add Option" to get started.
          </p>
        </div>
      ) : (
        options.map((option, optionIndex) => (
          <Card key={option.id} className="border-gray-200">
            <CardContent className="p-3 space-y-3">
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <GripVertical className="h-4 w-4 text-muted-foreground mr-2 cursor-grab" />
                  <h5 className="text-sm font-medium">
                    Option {optionIndex + 1}
                  </h5>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeOption(optionIndex)}
                >
                  <Trash className="h-3 w-3" />
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.value`}
                  label="Option Text"
                  placeholder="Enter option text"
                />

                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.externalId`}
                  label="External ID"
                  placeholder="Enter external ID"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.prompt`}
                  label="Prompt (Optional)"
                  placeholder="Enter prompt text"
                />

                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.color`}
                  label="Color (Optional)"
                  placeholder="e.g. #FF0000"
                  type="color"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.carePartnerValue`}
                  label="Care Partner Value (Optional)"
                  placeholder="Enter care partner value"
                />

                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.description`}
                  label="Description (Optional)"
                  placeholder="Enter description"
                />
              </div>

              <div className="flex items-center space-x-4 mt-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id={`${questionType}.${questionIndex}.options.${optionIndex}.endOfQuestions`}
                    {...control.register(
                      `${questionType}.${questionIndex}.options.${optionIndex}.endOfQuestions` as any
                    )}
                  />
                  <label
                    htmlFor={`${questionType}.${questionIndex}.options.${optionIndex}.endOfQuestions`}
                    className="text-sm"
                  >
                    End of Questions
                  </label>
                </div>
              </div>

              <OptionActions
                questionIndex={questionIndex}
                optionIndex={optionIndex}
                questionType={questionType}
              />
            </CardContent>
          </Card>
        ))
      )}
    </div>
  );
}

function RegularQuestionOptions({
  questionIndex,
  questionType = "questions",
}: QuestionOptionsProps) {
  const { control } = useFormContext();

  const {
    fields: options,
    append: appendOption,
    remove: removeOption,
  } = useFieldArray({
    control,
    name: `${questionType}.${questionIndex}.options` as any,
  });

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h4 className="font-medium">Options</h4>
        <Button
          type="button"
          size="sm"
          onClick={() =>
            appendOption({
              value: "",
              color: "",
              externalId: crypto.randomUUID(),
              actions: [],
            })
          }
        >
          <Plus className="mr-2 h-3 w-3" /> Add Option
        </Button>
      </div>

      {options.length === 0 ? (
        <div className="text-center p-4 border border-dashed rounded-lg">
          <p className="text-muted-foreground text-sm">
            No options added yet. Click "Add Option" to get started.
          </p>
        </div>
      ) : (
        options.map((option, optionIndex) => (
          <Card key={option.id} className="border-gray-200">
            <CardContent className="p-3 space-y-3">
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <GripVertical className="h-4 w-4 text-muted-foreground mr-2 cursor-grab" />
                  <h5 className="text-sm font-medium">
                    Option {optionIndex + 1}
                  </h5>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeOption(optionIndex)}
                >
                  <Trash className="h-3 w-3" />
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.value`}
                  label="Option Text"
                  placeholder="Enter option text"
                />

                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.externalId`}
                  label="External ID"
                  placeholder="Enter external ID"
                  disabled
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.prompt`}
                  label="Prompt (Optional)"
                  placeholder="Enter prompt text"
                />
              </div>
            </CardContent>
          </Card>
        ))
      )}
    </div>
  );
}

function LearnQuestionOptions({
  questionIndex,
  questionType = "learningQuestions",
}: QuestionOptionsProps) {
  const { control } = useFormContext();

  const {
    fields: options,
    append: appendOption,
    remove: removeOption,
  } = useFieldArray({
    control,
    name: `${questionType}.${questionIndex}.options` as any,
  });

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h4 className="font-medium">Options</h4>
        <Button
          type="button"
          size="sm"
          onClick={() =>
            appendOption({
              value: "",
              color: "",
              externalId: crypto.randomUUID(),
              actions: [],
            })
          }
        >
          <Plus className="mr-2 h-3 w-3" /> Add Option
        </Button>
      </div>

      {options.length === 0 ? (
        <div className="text-center p-4 border border-dashed rounded-lg">
          <p className="text-muted-foreground text-sm">
            No options added yet. Click "Add Option" to get started.
          </p>
        </div>
      ) : (
        options.map((option, optionIndex) => (
          <Card key={option.id} className="border-gray-200">
            <CardContent className="p-3 space-y-3">
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <GripVertical className="h-4 w-4 text-muted-foreground mr-2 cursor-grab" />
                  <h5 className="text-sm font-medium">
                    Option {optionIndex + 1}
                  </h5>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeOption(optionIndex)}
                >
                  <Trash className="h-3 w-3" />
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.value`}
                  label="Option Text"
                  placeholder="Enter option text"
                />

                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.externalId`}
                  label="External ID"
                  placeholder="Enter external ID"
                  disabled
                />

                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.color`}
                  label="Color"
                  placeholder="Select Color"
                  type="color"
                />

                <FormTextField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.description`}
                  label="Description"
                  placeholder="Enter prompt text"
                />
              </div>
            </CardContent>
          </Card>
        ))
      )}
    </div>
  );
}

function OptionActions({
  questionIndex,
  optionIndex,
  questionType = "questions",
}: OptionActionsProps) {
  const { control } = useFormContext();

  const {
    fields: actions,
    append: appendAction,
    remove: removeAction,
  } = useFieldArray({
    control,
    name: `${questionType}.${questionIndex}.options.${optionIndex}.actions` as any,
  });

  return (
    <div className="space-y-3">
      <div className="flex justify-between items-center">
        <h5 className="text-sm font-medium">Actions</h5>
        <Button
          type="button"
          size="sm"
          variant="outline"
          onClick={() =>
            appendAction({
              actionType: ActionTypes.NextQuestion,
              event: EventTypes.OptionSelect,
              metadata: {},
            })
          }
        >
          <Plus className="mr-2 h-3 w-3" /> Add Action
        </Button>
      </div>

      {actions.length === 0 ? (
        <div className="text-center p-3 border border-dashed rounded-lg">
          <p className="text-muted-foreground text-sm">
            No actions added yet. Click "Add Action" to get started.
          </p>
        </div>
      ) : (
        actions.map((action, actionIndex) => (
          <div key={action.id} className="border rounded-md p-3 space-y-3">
            <div className="flex justify-between items-center">
              <h6 className="text-xs font-medium">Action {actionIndex + 1}</h6>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => removeAction(actionIndex)}
              >
                <Trash className="h-3 w-3" />
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <FormSelect
                name={`${questionType}.${questionIndex}.options.${optionIndex}.actions.${actionIndex}.actionType`}
                label="Action Type"
                options={[
                  {
                    label: "Next Question",
                    value: ActionTypes.NextQuestion,
                  },
                  {
                    label: "Next Behaviour Question",
                    value: ActionTypes.NextBehaviourQuestion,
                  },
                  {
                    label: "Call",
                    value: ActionTypes.Call,
                  },
                ]}
              />

              <FormSelect
                name={`${questionType}.${questionIndex}.options.${optionIndex}.actions.${actionIndex}.event`}
                label="Event Type"
                options={[
                  {
                    label: "Option Select",
                    value: EventTypes.OptionSelect,
                  },
                  {
                    label: "On Inactive Schedule",
                    value: EventTypes.OnInactiveSchedule,
                  },
                ]}
              />
            </div>

            <ActionMetadata
              questionIndex={questionIndex}
              optionIndex={optionIndex}
              actionIndex={actionIndex}
              questionType={questionType}
            />
          </div>
        ))
      )}
    </div>
  );
}

function ActionMetadata({
  questionIndex,
  optionIndex,
  actionIndex,
  questionType = "questions",
}: ActionMetadataProps) {
  const { watch } = useFormContext();

  const actionType = watch(
    `${questionType}.${questionIndex}.options.${optionIndex}.actions.${actionIndex}.actionType` as any
  );

  if (actionType === ActionTypes.NextQuestion) {
    return (
      <div className="space-y-3">
        <FormField
          name={`${questionType}.${questionIndex}.options.${optionIndex}.actions.${actionIndex}.metadata.nextQuestionExternalId`}
          label="Next Question ID"
          placeholder="Enter next question ID"
        />
        <FormField
          name={`${questionType}.${questionIndex}.options.${optionIndex}.actions.${actionIndex}.metadata.optionExternalId`}
          label="Option External ID (use * for any option)"
          placeholder="Enter option external ID or *"
        />
      </div>
    );
  }

  if (actionType === ActionTypes.NextBehaviourQuestion) {
    return (
      <div className="space-y-3">
        <FormField
          name={`${questionType}.${questionIndex}.options.${optionIndex}.actions.${actionIndex}.metadata.nextQuestionExternalId`}
          label="Next Question ID"
          placeholder="Enter next question ID"
        />
        <FormField
          name={`${questionType}.${questionIndex}.options.${optionIndex}.actions.${actionIndex}.metadata.optionExternalId`}
          label="Option External ID"
          placeholder="Enter option external ID"
        />
        <FormField
          name={`${questionType}.${questionIndex}.options.${optionIndex}.actions.${actionIndex}.metadata.choosenFrequency`}
          label="Chosen Frequency"
          placeholder="Enter chosen frequency"
        />
        <FormField
          name={`${questionType}.${questionIndex}.options.${optionIndex}.actions.${actionIndex}.metadata.trackingWindow`}
          label="Tracking Window"
          placeholder="Enter tracking window"
          type="number"
        />
      </div>
    );
  }

  if (actionType === ActionTypes.Call) {
    return (
      <FormField
        name={`${questionType}.${questionIndex}.options.${optionIndex}.actions.${actionIndex}.metadata.phone`}
        label="Phone Number"
        placeholder="Enter phone number"
      />
    );
  }

  return (
    <div className="p-3 text-center text-sm text-muted-foreground">
      Select an action type to see additional options
    </div>
  );
}
