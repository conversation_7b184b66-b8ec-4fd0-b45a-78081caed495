import axios from "axios";

export const MIXPANEL_TOKEN = process.env.NEXT_PUBLIC_MIXPANEL_TOKEN!;

type Action = Record<string, string>;

interface MixpanelEventTypes {
  [key: string]: Action;
}

class MixpanelTracker {
  private token: string;

  private baseUrl = "https://api.mixpanel.com/track";

  constructor(token: string) {
    this.token = token;
  }

  trackEvent<T extends keyof MixpanelEventTypes>(
    event: T,
    properties: MixpanelEventTypes[T],
    distinct_id?: string | number
  ): Promise<any> {
    const data = {
      event,
      properties: {
        token: this.token,
        ...(distinct_id && { distinct_id }),
        ...properties,
      },
    };

    return axios.post(this.baseUrl, [data], {
      headers: { accept: "text/plain", "Content-Type": "application/json" },
      params: { verbose: "2" },
    });
  }
}

export default new MixpanelTracker(MIXPANEL_TOKEN);
