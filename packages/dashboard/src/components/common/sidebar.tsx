"use client";
import { cn } from "@web/lib/utils";
import {
  ActivitySquare,
  AlertTriangle,
  BookOpen,
  Home,
  Settings,
  BarChart,
} from "lucide-react";
import { useSession } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";

const links = [
  {
    href: "/",
    exact: true,
    icon: <Home className="text-white size-5" />,
    name: "Dashboard",
  },
  {
    href: "/services",
    icon: <ActivitySquare className="text-white size-5" />,
    name: "Services",
  },
  {
    href: "/blogs",
    icon: <BookOpen className="text-white size-5" />,
    name: "<PERSON>rn",
  },
  {
    href: "/reports",
    icon: <AlertTriangle className="text-white size-5" />,
    name: "Reports",
  },
  {
    href: "/utm",
    icon: <BarChart className="text-white size-5" />,
    name: "UTM",
  },
  {
    href: "/settings",
    icon: <Settings className="text-white size-5" />,
    name: "Settings",
  },
];

export default function SideBar() {
  const { data: session } = useSession();
  const path = usePathname();

  if (!session) return null;
  return (
    <div className="z-[20]">
      <div className="h-screen bg-primary flex flex-col items-start">
        {/* Logo */}
        <Link href="/" className="self-center">
          <Image
            width={40}
            height={40}
            src="/logo.svg"
            alt=""
            className="rounded-full mt-4"
          />
        </Link>
        {/* Menus */}
        <div className="mt-12 flex flex-col gap-4 z-10 w-full px-4">
          {links.map(({ href, icon, exact, name }, idx) => {
            let active = false;
            if (exact && href === path) active = true;
            if (!exact && path.startsWith(href)) active = true;

            return (
              <Link
                key={idx}
                href={href}
                className={cn(
                  "flex items-center gap-3 px-3 py-2 rounded-lg text-white transition-colors hover:bg-accent-foreground/50",
                  active && "bg-accent-foreground hover:bg-accent-foreground"
                )}
              >
                <span className="flex items-center justify-center">{icon}</span>
                <span className="text-sm font-medium">{name}</span>
              </Link>
            );
          })}
        </div>
      </div>
    </div>
  );
}
