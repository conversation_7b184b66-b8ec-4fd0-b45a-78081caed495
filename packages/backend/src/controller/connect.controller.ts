/* eslint-disable @typescript-eslint/ban-ts-comment */
import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import mongoose from 'mongoose';
import {
  createCommentValidator,
  createConnectPostValidator,
  deleteCommentValidator,
  editConnectPostValidator,
  getCommentValidator,
  getPostsOutPut,
  getPostsValidator,
  getPostValidator,
  likeCommentValidator,
  likeValidator,
  manageReportPostValidator,
  reportCommentValidator,
  reportPostValidator,
} from '../../../shared/validators/connect.validator';
import { CommentLikeModel, CommentModel, LikeModel, PostModel, ReportPostModel } from '../models/Connect';
import { MID } from '../@types/common';
import { pagination, removeFileFromS3 } from '../utils';
import mixpanel from '../utils/mixpanel';
import log from '../utils/logger';
import { getPostsAggregation } from '../models/aggregations/connect.aggregation';
import { ReportPostStatus } from '../../../shared/types/Connect';

export const createPost = (payload: z.infer<typeof createConnectPostValidator>, user: MID) => new PostModel({ ...payload, user }).save();

export const editPost = async (payload: z.infer<typeof editConnectPostValidator>, user: MID) => {
  const { postId, ...restPayload } = payload;
  console.log({ restPayload, postId })
  const existingPost = await PostModel.findById(postId);
  console.log({ existingPost })
  if (!existingPost) {
    throw new TRPCError({ code: 'NOT_FOUND', message: 'Post not found' });
  }
  const updatedPost = await PostModel.findByIdAndUpdate(postId, restPayload, {
    new: true,
  });

  return updatedPost;
};

export const getPost = (postId: z.infer<typeof getPostValidator>) => PostModel.findById(postId).populate({ path: 'user' }).lean();

export const deletePost = async (postId: z.infer<typeof getPostValidator>) => {
  const post = await PostModel.findById(postId);
  if (!post) throw new TRPCError({ code: 'NOT_FOUND', message: 'post not found' });

  post.disabled = true;
  await post.save();
  if (post.image) {
    removeFileFromS3({ key: post.image }).catch((err) => {
      log.error('@PostImageRemoveError', err);
    });
  }

  // mixpanel event
  mixpanel.trackEvent('DELETE_POST', { createdAt: String(new Date()), postId: String(post._id), post: post.title || '' }, String(post.user));
  return post.toJSON();
};

export const reportPost = async (postId: z.infer<typeof reportPostValidator>, userId: MID) => {
  // find a post
  const post = await PostModel.findById(postId);
  if (!post) throw new TRPCError({ code: 'NOT_FOUND', message: 'post not found' });

  if (post.user.toString() === userId) throw new TRPCError({ code: 'FORBIDDEN', message: "can't report your own post" });

  const reportPostDocument = await ReportPostModel.findOneAndUpdate(
    { post: post.id, reportedBy: userId, type: 'post' },
    { post: post.id, reportedBy: userId, type: 'post' },
    { upsert: true, new: true },
  );

  reportPostDocument.populate([{ path: 'post', populate: { path: 'user' } }, { path: 'reportedBy' }]).then((_post: any) => {
    mixpanel.trackEvent(
      'REPORT_POST',
      {
        createdAt: new Date().toISOString(),
        postAuthorUsername: _post?.post?.user?.username,
        postId: _post?.post?.user?._id?.toString(),
        reporterUsername: _post?.reportedBy?.username,
        imgUrl: _post?.post?.image ? `https://${process.env.S3_BUCKET}.s3.us-east-2.amazonaws.com/${_post?.post?.image}` : undefined,
        post: _post?.post?.title,
      },
      String(userId),
    );
  });

  return reportPostDocument;
};

export const likePost = ({ post, status }: z.infer<typeof likeValidator>, user: MID) =>
  status ? LikeModel.findOneAndUpdate({ post, user }, { post, user }, { upsert: true, new: true }) : LikeModel.findOneAndDelete({ post, user });

export const getComments = ({ post }: z.infer<typeof getCommentValidator>, user: MID) =>
  CommentModel.aggregate([
    {
      $match: {
        post: new mongoose.Types.ObjectId(post),
      },
    },
    {
      $sort: {
        createdAt: -1,
      },
    },
    {
      $lookup: {
        from: 'comment-likes',
        localField: '_id',
        foreignField: 'comment',
        as: 'likes',
      },
    },
    {
      $lookup: {
        from: 'user-accounts',
        localField: 'user',
        foreignField: '_id',
        as: 'user',
      },
    },
    {
      $unwind: {
        path: '$user',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $addFields: {
        isLiked: {
          $cond: {
            if: !!user,
            then: {
              $in: [new mongoose.Types.ObjectId(user), '$likes.user'],
            },
            else: false,
          },
        },
        likes: {
          $size: '$likes',
        },
      },
    },
  ]);

export const createComment = async ({ comment, post }: z.infer<typeof createCommentValidator>, user: MID) => {
  const isPostExist = await PostModel.exists({ _id: post });
  if (!isPostExist) throw new TRPCError({ code: 'NOT_FOUND', message: 'post not found' });

  return new CommentModel({ comment, post, user }).save();
};

export const reportComment = async ({ comment }: z.infer<typeof reportCommentValidator>, user: MID) => {
  const commentDoc = await CommentModel.findById(comment);
  if (!commentDoc) throw new TRPCError({ code: 'NOT_FOUND', message: 'comment not found' });

  if (commentDoc.user.toString() === user.toString()) throw new TRPCError({ code: 'FORBIDDEN', message: "can't report your own comment" });
  const rc = await ReportPostModel.findOneAndUpdate(
    { comment, reportedBy: user, type: 'comment' },
    { comment, reportedBy: user, type: 'comment' },
    { upsert: true, new: true },
  );

  commentDoc
    .populate([
      { path: 'comment', populate: { path: 'user' } },
      { path: 'post', populate: { path: 'user' } },
    ])
    .then((_post: any) => {
      mixpanel.trackEvent(
        'REPORT_COMMENT',
        {
          comment: _post?.comment?.comment as string,
          commentAuthor: _post?.comment?.user?.username,
          createdAt: new Date().toISOString(),
          postAuthor: _post?.post?.user?.username,
          postId: _post?.post?.user?._id?.toString(),
          post: _post?.post?.title,
        },
        String(user),
      );
    });

  return rc;
};

export const manageReportPost = async ({ status, reportId }: z.infer<typeof manageReportPostValidator>) => {
  const report = await ReportPostModel.findById(reportId);
  if (!report) throw new TRPCError({ code: 'NOT_FOUND', message: 'report not found' });

  if (status === ReportPostStatus.APPROVE) {
    if (report.type === 'comment') {
      await CommentModel.findByIdAndDelete(report.comment);
    }
    if (report.type === 'post') {
      await PostModel.findByIdAndUpdate(report.post, { disabled: true });
    }
  }

  return ReportPostModel.findByIdAndUpdate(reportId, { status }, { new: true });
};

export const deleteComment = async (commentId: z.infer<typeof deleteCommentValidator>, userId: MID) => {
  const comment = await CommentModel.findById(commentId);
  if (!comment) throw new TRPCError({ code: 'NOT_FOUND', message: 'comment not found' });

  // if (comment.user.toString() !== userId.toString()) throw new TRPCError({ code: 'FORBIDDEN', message: "can't delete other's comment" });

  mixpanel.trackEvent(
    'DELETE_COMMENT',
    { createdAt: String(new Date()), postId: String(comment.post), comment: comment.comment },
    String(comment.user),
  );
  await CommentModel.deleteOne({ _id: commentId });
};

export const getPosts = async (payload: z.infer<typeof getPostsValidator>): Promise<z.infer<typeof getPostsOutPut>> => {
  const { cursor, limit } = payload;
  // @ts-ignore
  const posts = (await PostModel.aggregate(getPostsAggregation(payload)))?.[0];

  // Transform
  posts.totalPostsCount = posts.totalPostsCount?.[0]?.totalCount ?? 0;

  const paginationResults = pagination(cursor, posts.totalPostsCount as number, limit);

  delete posts.totalPostsCount;
  return { ...posts, pagination: paginationResults };
};

export const likeComment = ({ comment, status }: z.infer<typeof likeCommentValidator>, user: MID) => {
  mixpanel.trackEvent(status ? 'LIKE_COMMENT' : 'UNLIKE_COMMENT', { createdAt: String(new Date()), commentId: String(comment) }, String(user));
  if (status) return CommentLikeModel.findOneAndUpdate({ comment, user }, { comment, user }, { upsert: true, new: true });
  return CommentLikeModel.findOneAndDelete({ comment, user });
};
