import { z } from 'zod';
import { FilterQuery } from 'mongoose';
import {
  createBlogTagValidator,
  manageBlogValidator,
  getBlogsValidator,
  updateBlogTagValidator,
  manageBlogsDashboardValidator,
  getBlogValidator,
} from '../../../shared/validators/blog';
import { BlogDashboardModel, BlogModel, BlogTagModel } from '../models/Blog';
import { Blog } from '../../../shared/types/Blog';
import { TRPCError } from '@trpc/server';

export const getBlogTags = () => BlogTagModel.find().lean();
export const deleteBlogTag = async (blogTagId: string) => {
  const blogTag = await BlogTagModel.exists({ _id: blogTagId });
  if (!blogTag) throw new TRPCError({ code: 'NOT_FOUND' });

  await BlogTagModel.findByIdAndDelete(blogTagId);
  await BlogModel.updateMany({}, { $pull: { tags: blogTagId } });

  return { status: 'ok' };
};
export const createBlogTag = (tag: z.infer<typeof createBlogTagValidator>) => new BlogTagModel(tag).save();
export const updateBlogTag = ({ tag, tagId }: z.infer<typeof updateBlogTagValidator>) => BlogTagModel.findByIdAndUpdate(tagId, { tag });

export const manageBlog = async (blog: z.infer<typeof manageBlogValidator>) => {
  const { blogId, ...rest } = blog;
  if (blogId) await BlogModel.findByIdAndUpdate(blogId, { ...rest });
  else new BlogModel(rest).save();
};

export const getBlogs = (filter: z.infer<typeof getBlogsValidator>) => {
  let query: FilterQuery<Blog> = {};
  if (filter) {
    const { tags, diseaseTags, ...other } = filter;
    query = { ...other, ...(!!tags?.length && { tags: { $in: tags } }), ...(!!diseaseTags?.length && { diseaseTags: { $in: diseaseTags } }) };
  }

  return BlogModel.find(query)
    .populate<{ tags?: { _id: string; tag: string }[] }>([{ path: 'tags' }])
    .sort({ createdAt: -1 })
    .lean();
};

export const getBlog = ({ blog }: z.infer<typeof getBlogValidator>) => BlogModel.findById(blog);

export const manageBlogDashboard = async (payload: z.infer<typeof manageBlogsDashboardValidator>) =>
  BlogDashboardModel.updateOne({}, payload, { upsert: true });
