import { Types } from 'mongoose';
import { z } from 'zod';
import {
  createCategoryValidator,
  createOrUpdateServiceValidator,
  updateServiceSequenceValidator,
} from '../../../shared/validators/service.validator';
import { ServiceCategoryModel, ServiceModel } from '../models/Service';

export const createCategory = (payload: z.infer<typeof createCategoryValidator>) => new ServiceCategoryModel(payload).save();
export const createUpdateService = async ({ _id, ...payload }: z.infer<typeof createOrUpdateServiceValidator>) => {
  // @ts-ignore
  if (!_id) await ServiceModel.create({ ...payload, category: [...new Set(payload.category)] });
  // @ts-ignore
  else await ServiceModel.findOneAndUpdate({ _id }, { ...payload, category: [...new Set(payload.category)] }, { upsert: true });

  return { status: 'OK' };
};

export const updateServiceSequence = async (payload: z.infer<typeof updateServiceSequenceValidator>) => {
  const bulkOperation = payload.map((data) => ({
    updateOne: {
      filter: { _id: new Types.ObjectId(data.serviceId) },
      update: { $set: { sequence: data.sequence } },
    },
  }));

  await ServiceModel.bulkWrite(bulkOperation);

  return { status: 'OK' };
};
