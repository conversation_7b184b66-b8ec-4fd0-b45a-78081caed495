import TopicModel from '../models/Topics';

TopicModel.updateOne(
  { _id: '6703f2a9ab6dd1cba828a588' },
  {
    $set: {
      name: 'Asthma',
      nickname: 'Asthma',
      externalId: '111',
      category: 'Respiratory',
      tags: ['Asthma'],
      questions: [
        {
          _id: '672b9164a1b35aa1fc17b785',
          question: 'How are you connected to asthma?',
          externalId: '111-12',
          type: 'select',
          options: [
            {
              _id: '672b91a716b2ad7f5b749775',
              value: 'I have asthma',
              prompt: 'Got it!  Let’s make managing it easier together!',
              externalId: '111-12-1',
            },
            {
              _id: '672b917533d5cc5cc4a73360',
              value: 'I am supporting someone with asthma',
              prompt: 'Amazing!  I’m designed to help you support them at every step.',
              externalId: '111-12-2',
            },
            {
              _id: '67856c2dfe3d9a31bd64c031',
              value: 'I am a Healthcare Professional',
              prompt: 'Great!  Let’s get to work to empower your patients.',
              externalId: '111-12-3',
            },
          ],
        },
        // {
        //   _id: '672b8b8ffa5431b4c52d4fc2',
        //   question: 'How did you hear about myRabble?',
        //   externalId: '111-11',
        //   type: 'select',
        //   options: [
        //     {
        //       _id: '672b8eff66821551c1df511d',
        //       value: 'Doctor',
        //       externalId: '111-11-1',
        //     },

        //     {
        //       _id: '672b8f3ef71d437737e100c2',
        //       value: 'Family or Friends',
        //       externalId: '111-11-2',
        //     },

        //     {
        //       _id: '672b8fb3d22680bded087b89',
        //       value: 'Other Patients or Care Partners',
        //       externalId: '111-11-3',
        //     },
        //     {
        //       _id: '67856aad06e15aeee701dd12',
        //       value: 'Google Search',
        //       externalId: '111-11-4',
        //     },
        //     {
        //       _id: '67856af7a67b6d4f32dd7a02',
        //       value: 'Instagram / Facebook / Threads',
        //       externalId: '111-11-5',
        //     },
        //     {
        //       _id: '67856afe7e18a20e93de4085',
        //       value: 'News Article / Blog',
        //       externalId: '111-11-6',
        //     },
        //     {
        //       _id: '67856b09633349b924bb660f',
        //       value: 'Other',
        //       externalId: '111-11-7',
        //     },
        //   ],
        // },
        // {
        //   _id: '672b9164a1b35aa1fc17b785',
        //   question: 'Why are you focused on asthma?',
        //   externalId: '111-12',
        //   type: 'select',
        //   options: [
        //     {
        //       _id: '672b91a716b2ad7f5b749775',
        //       value: 'to manage my asthma better',
        //       prompt: 'Got it!  Let’s make managing it easier together!',
        //       externalId: '111-12-1',
        //     },
        //     {
        //       _id: '672b917533d5cc5cc4a73360',
        //       value: 'to support someone facing asthma',
        //       prompt: 'Amazing!  I’m designed to help you support them at every step.',
        //       externalId: '111-12-2',
        //     },
        //     {
        //       _id: '67856c2dfe3d9a31bd64c031',
        //       value: 'I am a Healthcare Provider',
        //       prompt: 'Great!  Let’s get to work to empower your patients.',
        //       externalId: '111-12-3',
        //     },
        //   ],
        // },
        // {
        //   _id: '672b92461f275f797b4a305c',
        //   question: 'What describes your experience with Asthma?',
        //   externalId: '111-13',
        //   type: 'select',
        //   options: [
        //     {
        //       _id: '672b92559d147489160ad2cc',
        //       value: 'new to asthma',
        //       prompt: 'Great! We’ll start fresh!',
        //       externalId: '111-13-1',
        //     },
        //     {
        //       _id: '672b9263056fe588ebc218ec',
        //       value: 'basic knowledge',
        //       prompt: 'Ok! We’ll build on what you know!',
        //       externalId: '111-13-2',
        //     },
        //     {
        //       _id: '672b9270609c64b40e98142f',
        //       value: 'well-experienced',
        //       prompt: "Wow, that’s great! If you have ideas to make it better for others, we'd love to listen.",
        //       externalId: '111-13-3',
        //     },
        //     // {
        //     //   _id: '67856e0ea97e6cecb12ee87b',
        //     //   value: 'I’m an expert',
        //     //   prompt: 'Wow! That’s great!',
        //     //   externalId: '111-13-4',
        //     // },
        //   ],
        // },
        // {
        //   _id: '67856f299cc16fcc9db0ea21',
        //   question: 'What should be our goals?',
        //   externalId: '111-14',
        //   type: 'MultiSelect',
        //   options: [
        //     {
        //       _id: '67856f3109f04fe0a46a5330',
        //       value: 'to manage asthma better',
        //       externalId: '111-14-1',
        //     },
        //     {
        //       _id: '67856f39fa2ca8d432415092',
        //       value: 'to learn more',
        //       externalId: '111-14-2',
        //     },
        //     {
        //       _id: '67856f42435772caae2c22e0',
        //       value: 'to connect with others',
        //       externalId: '111-14-3',
        //     },
        //     {
        //       _id: '67856f481179ac8948b13f3a',
        //       value: 'to access services',
        //       externalId: '111-14-4',
        //     },
        //   ],
        // },
      ],
      learningQuestions:  [
        {
          _id: "67979d8eccb0470b5551ab3a",
          question: "Learn about tracking asthma zones",
          type: "select",
          externalId: "111-21",
          options: [
            {
              _id: "67979df5d9127e2de2ebf4d9",
              value: "Green Zone 😊",
              description: `What is the Green Zone for Asthma?
      The Green Zone means your asthma is doing great. You can breathe easily, and you're not having any trouble with your chest, coughing, or wheezing.
      Here's what it looks like when you're in the Green Zone:
          • You can run and play without coughing or\n      getting tired too fast
          • You're not waking up at night because of\n      your asthma
          • You don't need your rescue inhaler (the one\n      that helps you breathe during an attack)
          • Your breathing feels normal and comfortable
      When you're in the Green Zone, you:
          • Take your daily asthma medicine (the controller)\n      just like your doctor says—even if you feel good!
          • Keep doing your normal activities, like school,\n      sports, and playing with friends
      Think of it like a traffic light:
          • 🟢 Green = Go! Everything is good
          • 🟡 Yellow = Slow down. Pay attention to your asthma
          • 🔴 Red = Stop! You need help right away
      Staying in the Green Zone is the goal! It means your asthma is under control and you're feeling your best.`,
              externalId: "111-21-1",
              color: "#25BA60"
            },
            {
              _id: "67979df5d9127e2de2ebf4da",
              value: "Yellow Zone 😐",
              description: `The Yellow Zone means your asthma is acting up a little. You might not feel your best, and breathing is a bit harder than usual. It's a warning to slow down and take action so things don't get worse.
      Here's what it looks like when you're in the Yellow Zone:
          • You're coughing, wheezing, or your chest\n      feels tight
          • You're having a harder time breathing than\n      normal
          • You're waking up at night because of asthma
          • You can't keep up with playing, running, or\n      other activities like you usually do
          • You need to use your rescue inhaler (the\n      quick-relief one)
      When you're in the Yellow Zone, you:
          • Use your rescue inhaler just like your doctor\n      or asthma plan says
          • Let a grown-up know how you're feeling
          • Rest and avoid things that make your asthma\n      worse (like smoke, dust, or running too much)
      Remember the traffic light:
          • 🟢 Green = Go! You're feeling good
          • 🟡 Yellow = Slow down! Asthma is getting worse
          • 🔴 Red = Stop! Get help now
      The Yellow Zone is your body's way of saying, "Hey, I need a little help!" If you act early, you can get back to the Green Zone faster.`,
              externalId: "111-21-2",
              color: "#FF8E1C"
            },
            {
              _id: "67979df5d9127e2de2ebf4db",
              value: "Danger Zone 😟",
              description: `The Red Zone means asthma is very serious right now. Breathing is hard, and medicine may not be working. It's time to get help fast.
      Here are signs you're in the Red Zone:
          • You feel very short of breath, even when\n      resting
          • You are coughing a lot or your chest feels\n      tight
          • You are wheezing or making it hard to speak\n      clearly
          • You used your rescue inhaler, but it's not\n      helping
          • Your lips or fingernails may look blue or gray
      What to do:
          • Use your quick-relief inhaler right away
          • Tell someone or call for help
          • Call 911 or go to the emergency room if you\n      are not getting better quickly
      Think of a stoplight:
          • 🟢 Green = Everything is good
          • 🟡 Yellow = Asthma is starting to get worse
          • 🔴 Red = Danger. Get help now
      The Red Zone is an emergency. Act fast to stay safe.`,
              externalId: "111-21-3",
              color: "#E54D4D"
            }
          ]
        }
      ],
      dailyTrackerQuestions: [
        {
          _id: '672c9b088972d5962a2c44d8',
          question: 'How would you rate your asthma control over the last 4 weeks?',
          carePartnerText: "How would you rate the patient's asthma control over the last 4 weeks?",
          externalId: '111-54',
          isSchedule: true,
          delay: 28,
          duration: 0,
          recurringQuestion: true,
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '*',
                nextQuestionExternalId: '111-55',
              },
            },
          ],
          options: [
            {
              _id: '672c9b0c36a0db9dae5574f2',
              value: '1 😠',
              externalId: '111-54-1',
            },
            {
              _id: '672c9b151b9e66c1dd655fcc',
              value: '2',
              externalId: '111-54-2',
            },
            {
              _id: '67452ea483ef0462888dae34',
              value: '3 😐',
              externalId: '111-54-3',
            },
            {
              _id: '67452e9de0ca74eeb1fc5fa8',
              value: '4',
              externalId: '111-54-4',
            },
            {
              _id: '67452e954b3df74b870ae959',
              value: '5 😊',
              externalId: '111-54-5',
            },
          ],
        },
        {
          _id: '673ca3b31c1bd160fda8eec1',
          question: 'What type of provider do you see for your asthma?',
          type: 'MultiSelect',
          carePartnerText: 'What type of provider does the patient see for asthma?',
          externalId: '111-67',
          isSchedule: true,
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '*',
                nextQuestionExternalId: '111-51',
              },
            },
          ],
          options: [
            {
              _id: '673ca2cf86965d05571faaa0',
              value: 'Primary Care/Family Practice',
              externalId: '111-67-1',
            },
            {
              _id: '673ca2d94e5fe27a02a7f9be',
              value: 'Pediatrician',
              externalId: '111-67-2',
            },
            {
              _id: '673ca2e1c3b30cfa64167f50',
              value: 'Pulmonologist',
              externalId: '111-67-3',
            },
            {
              _id: '673ca2ed639c46c8a3d63965',
              value: 'Urgent Care provider',
              externalId: '111-67-4',
            },
            {
              _id: '673ca2f660cee1c5600b0e9e',
              value: 'Allergist',
              externalId: '111-67-5',
            },
            {
              _id: '673ca2fd30de6c266983e920',
              value: 'Immunnologist',
              externalId: '111-67-6',
            },
            {
              _id: '673ca304156003b856a64f1e',
              value: 'Emergency room providers',
              externalId: '111-67-7',
            },
            {
              _id: '673ca30add91c5105ee044c1',
              value: 'Rheumotologist',
              externalId: '111-67-8',
            },
            {
              _id: '673ca3114937df729a8e2bfb',
              value: 'Ear, Nose, Throat',
              externalId: '111-67-9',
            },
            {
              _id: '673ca31fc48a1fe943f49167',
              value: 'Gastroentorologist',
              externalId: '111-67-10',
            },
          ],
        },

        {
          _id: '672c4b677fe069471a1a9ee1',
          question: 'How is your asthma today?',
          carePartnerText: 'How is their asthma today?',
          externalId: '111-55',
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-55-1',
                nextQuestionExternalId: '111-52',
              },
            },
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-55-2',
                nextQuestionExternalId: '111-56',
              },
            },
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-55-3',
                nextQuestionExternalId: '111-68',
              },
            },
          ],
          options: [
            {
              _id: '672c4bfe65e70fb062ec4534',
              value: 'Go Zone 🙂',
              externalId: '111-55-1',
              color: '#25BA60',
            },
            {
              _id: '672c4c86fecfa0affe1e8876',
              value: 'Caution Zone 🫤',
              externalId: '111-55-2',
              color: '#FFBE1C',
            },
            {
              _id: '672c4c8e3f5ab9a1d13f964c',
              value: 'Danger Zone 😔',
              externalId: '111-55-3',
              color: '#E54D4D',
            },
          ],
        },

        {
          _id: '672c4f5630ce98a691e5e3af',
          question: 'What symptoms are you experiencing?',
          carePartnerText: 'What symptoms is the patient experiencing?',
          type: 'MultiSelect',
          externalId: '111-56',
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '*',
                nextQuestionExternalId: '111-57',
              },
            },
          ],
          options: [
            {
              _id: '672c4f651e759289b080f1bb',
              value: 'None',
              externalId: '111-56-0',
            },
            {
              _id: '672c4f651e759289b080f1cb',
              value: 'Wheeze',
              externalId: '111-56-1',
            },
            {
              _id: '672c4f6c716a593c20921446',
              value: 'Cough',
              externalId: '111-56-2',
            },
            {
              _id: '672c4f7283a613e2a4afff03',
              value: 'Shortness of Breath',
              externalId: '111-56-3',
            },
            {
              _id: '672c4f7796dd982066204fc4',
              value: 'Activity limitation',
              externalId: '111-56-4',
            },
            {
              _id: '672c4f7c4f85ee50d372024a',
              value: 'Waking up due to asthma',
              externalId: '111-56-5',
            },
            {
              _id: '672c4f8181b372f57341bf7d',
              value: 'Drop in peak flow',
              externalId: '111-56-6',
            },
            {
              _id: '672c4f867fcea635256dc18a',
              value: 'Increased use of rescue inhaler',
              externalId: '111-56-7',
            },
          ],
        },
        {
          _id: '672c51c461edf9a3af6306fb',
          question: 'Did you recognize any triggers?',
          carePartnerText: 'Did the patient recognize any triggers?',
          type: 'MultiSelect',
          externalId: '111-57',
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '*',
                nextQuestionExternalId: '111-58',
              },
            },
          ],
          options: [
            {
              _id: '672c51cb6f7c27dcebbbe34a',
              value: 'Weather',
              externalId: '111-57-1',
            },
            {
              _id: '672c51d01f22e25e80d97886',
              value: 'Medicine',
              externalId: '111-57-2',
            },
            {
              _id: '672c51d675d6d09ec3afce5d',
              value: 'Pollution',
              externalId: '111-57-3',
            },
            {
              _id: '672c51dc09970531de6cd05f',
              value: 'Emotion',
              externalId: '111-57-4',
            },
            {
              _id: '672c51e3ea5a7b59b3ad4bc9',
              value: 'Exercise',
              externalId: '111-57-5',
            },
            {
              _id: '672c51e89a01de458fb5bd78',
              value: 'Allergens',
              externalId: '111-57-6',
            },
            {
              _id: '672c51eeb6757c0ad4b9e71f',
              value: 'Illness',
              externalId: '111-57-7',
            },
          ],
        },
        {
          _id: '672c521a2e5a541010b1445e',
          question: 'Where were you?',
          carePartnerText: 'Where was the patient?',
          type: 'MultiSelect',
          externalId: '111-58',
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '*',
                nextQuestionExternalId: '111-59',
              },
            },
          ],
          options: [
            {
              _id: '672c522061633362fbbf7967',
              value: 'Home',
              externalId: '111-58-1',
            },
            {
              _id: '672c5226e9d639b0ead69466',
              value: 'Work',
              externalId: '111-58-2',
            },
            {
              _id: '672c522c5e54465861791fa2',
              value: 'School',
              externalId: '111-58-3',
            },
            {
              _id: '672c523a6a0357e14cea9df4',
              value: 'Outside',
              externalId: '111-58-4',
            },
            {
              _id: '672c52426a570ba3bef86e2c',
              value: 'Other',
              externalId: '111-58-5',
            },
          ],
        },
        {
          _id: '672c54f2d5e0c223c5a4abb0',
          question: 'Did you have an unplanned visit for care?',
          carePartnerText: 'Did the patient have an unplanned visit for care?',
          externalId: '111-59',
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '*',
                nextQuestionExternalId: '111-60',
              },
            },
          ],
          options: [
            {
              _id: '672c54bdc5af0c287ac4c78a',
              value: 'Yes',
              externalId: '111-59-1',
            },
            {
              _id: '672c54c3bfb92de2f1bce672',
              value: 'No',
              externalId: '111-59-2',
            },
          ],
        },
        {
          _id: '672c5528094af7a559e1ce26',
          question: 'Did you miss work or school?',
          carePartnerText: 'Did the patient miss school or work?',
          externalId: '111-60',
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '*',
                nextQuestionExternalId: '111-61',
              },
            },
          ],
          options: [
            {
              _id: '672c553577580f67ac504ee6',
              value: 'Yes',
              externalId: '111-60-1',
            },
            {
              _id: '672c55399f3f07f104a59a44',
              value: 'No',
              externalId: '111-60-2',
            },
          ],
        },
        {
          _id: '672c56c9faabacff8ca4ce6f',
          question: 'Did you change your meds?',
          carePartnerText: 'Did the patient change their meds?',
          type: 'MultiSelect',
          externalId: '111-61',
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '*',
                nextQuestionExternalId: '111-52',
              },
            },
          ],
          options: [
            {
              _id: '672c56ecae9d60d939fa7913',
              value: 'No',
              externalId: '111-61-6',
            },
            {
              _id: '672c56ce8be73ae1618a3f70',
              value: 'Added an inhaler',
              externalId: '111-61-1',
            },
            {
              _id: '672c56d5d09742007b35423e',
              value: 'Nebulized',
              externalId: '111-61-2',
            },
            {
              _id: '672c56daee118cc02ade0251',
              value: 'Started an inhaled corticosteroid',
              externalId: '111-61-3',
            },
            {
              _id: '672c56e0250a9a40e1d29f89',
              value: 'Started a round of oral steroids',
              externalId: '111-61-4',
            },
            {
              _id: '672c56e7649ddbd4ce9947b3',
              value: 'Added Biologics',
              externalId: '111-61-5',
            },
          ],
        },
        {
          _id: '672c5890b67d956bf7a89334',
          question: 'Check all that apply',
          carePartnerText: 'Check all that apply',
          type: 'MultiSelect',
          externalId: '111-62',
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '*',
                nextQuestionExternalId: '111-52',
              },
            },
          ],
          options: [
            {
              _id: '672c58995946b69988de433f',
              value: 'I went to the emergency room',
              carePartnerValue: 'The patient went to the emergency room',
              externalId: '111-62-1',
            },
            {
              _id: '672c58a00fca4d03d2500eb9',
              value: 'I was admitted to the hospital',
              carePartnerValue: 'The patient was admitted to the hospital',
              externalId: '111-62-2',
            },
            {
              _id: '672c58a47df1e24d1e5d74bb',
              value: 'I took oral or iv steroids',
              carePartnerValue: 'The patient took oral or iv steroids',
              externalId: '111-62-3',
            },
            {
              _id: '672c58a47df1e24d1e5d74bc',
              value: 'I had a change in my inhaler',
              carePartnerValue: 'The patient had a change in inhaler',
              externalId: '111-62-6',
            },
            {
              _id: '672c58a47df1e24d1e5d74bd',
              value: 'I had a change in antibiotics',
              carePartnerValue: 'I had a change in antibiotics',
              externalId: '111-62-7',
            },
            {
              _id: '672c58a968f3582218c62bc6',
              value: 'A respiratory therapist helped my with my medicine',
              carePartnerValue: 'A respiratory therapist helped the patient with medicine',
              externalId: '111-62-4',
            },
            {
              _id: '672c58ae6d2a2c0bbbb33bc0',
              value: 'Something else',
              externalId: '111-62-5',
            },
          ],
        },
        {
          _id: '674749ce204ddbafe3b1807c',
          question: 'Identify symptoms and dial 911',
          carePartnerText: 'Identify symptoms and dial 911',
          externalId: '111-68',
          type: 'MultiSelect',
          actions: [
            {
              actionType: 'Call',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-68-1',
                phone: '911',
              },
            },
            {
              actionType: 'Call',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-68-1',
                phone: '911',
              },
            },
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '*',
                nextQuestionExternalId: '111-62',
              },
            },
          ],
          options: [
            {
              _id: '67bf57664f00651415c385c3',
              value: 'Wheeze',
              carePartnerValue: 'Wheeze',
              externalId: '111-68-3',
            },
            {
              _id: '67bf575a08a6be9950c1c308',
              value: 'Cough',
              carePartnerValue: 'Cough',
              externalId: '111-68-4',
            },
            {
              _id: '67bf5750de220128852f2e44',
              value: 'Shortness of Breath',
              carePartnerValue: 'Shortness of Breath',
              externalId: '111-68-5',
            },
            {
              _id: '67bf57492eaf42465aec7144',
              value: 'Activity limitation',
              carePartnerValue: 'Activity limitation',
              externalId: '111-68-6',
            },
            {
              _id: '67bf57404735d622f1a24af0',
              value: 'Waking up due to asthma',
              carePartnerValue: 'Waking up due to asthma',
              externalId: '111-68-7',
            },
            {
              _id: '67bf573a1994984408e74f09',
              value: 'Drop in peak flow',
              carePartnerValue: 'Drop in peak flow',
              externalId: '111-68-8',
            },
            {
              _id: '67bf57334b20c8dc38a11d46',
              value: 'Increased use of rescue inhaler',
              carePartnerValue: 'Increased use of rescue inhaler',
              externalId: '111-68-9',
            },
            {
              _id: '674749dcb1a3269788c81380',
              value: 'Call 911',
              externalId: '111-68-1',
            },
            {
              _id: '674749e52feae78a30d451fe',
              value: "I'm Okay",
              carePartnerValue: "We're Okay",
              externalId: '111-68-2',
            },
          ],
        },

        {
          _id: '672c7eb9d2e36666d636e6ab',
          question: 'What severity of asthma do you have?',
          carePartnerText: 'What severity of asthma does the patient have?',
          externalId: '111-52',
          isSchedule: true,
          delay: 1,
          duration: 30,
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-52-1',
                nextQuestionExternalId: '111-53',
              },
            },
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-52-2',
                nextQuestionExternalId: '111-53',
              },
            },
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-52-3',
                nextQuestionExternalId: '111-53',
              },
            },
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-52-4',
                nextQuestionExternalId: '111-53',
              },
            },
          ],
          options: [
            {
              _id: '672c7ec29c5b553cfc04beff',
              value: 'Mild',
              externalId: '111-52-1',
            },
            {
              _id: '672c7ecc340e6121becf4119',
              value: 'Moderate',
              externalId: '111-52-2',
            },
            {
              _id: '672c7ed4f992605d909aef1d',
              value: 'Severe',
              externalId: '111-52-3',
            },
            {
              _id: '67f48c2b6e42f3e5923b0630',
              value: "Don't know",
              externalId: '111-52-4',
            },
          ],
        },
        {
          _id: '672c99e76c300a9c6eae79cf',
          question: 'What type of asthma do you have?',
          type: 'MultiSelect',
          carePartnerText: 'What type of asthma does the patient have?',
          externalId: '111-53',
          isSchedule: true,
          delay: 1,
          duration: 30,
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-53-1',
                nextQuestionExternalId: '111-67',
              },
            },
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-53-2',
                nextQuestionExternalId: '111-67',
              },
            },
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-53-3',
                nextQuestionExternalId: '111-67',
              },
            },
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-53-4',
                nextQuestionExternalId: '111-67',
              },
            },
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-53-5',
                nextQuestionExternalId: '111-67',
              },
            },
          ],
          options: [
            {
              _id: '672c98a001a02c3c6cdc848e',
              value: 'Allergic asthma (sometimes called rhinitis or Type 2 inflamation)',
              externalId: '111-53-1',
            },
            {
              _id: '672c98a94fee638c2a54a2f0',
              value: 'Non-allergic',
              externalId: '111-53-2',
            },
            {
              _id: '672c98b019980718babeb438',
              value: 'Exercised induced',
              externalId: '111-53-3',
            },
            {
              _id: '672c98b63d0d0b7e2f86c4a6',
              value: 'Nighttime asthma',
              externalId: '111-53-4',
            },
            {
              _id: '672c98bd044623b006d6a7ae',
              value: 'Eosinophilic asthma (sometimes called type 2 inflammation)',
              externalId: '111-53-5',
            },
            {
              _id: '672c98c4999110def75ab60e',
              value: "Don't know",
              externalId: '111-53-6',
            },
          ],
        },
        {
          _id: '672c7de0d587f6487df2ee89',
          question: 'Do you have an asthma action plan?',
          carePartnerText: 'Does the patient have an asthma action plan?',
          externalId: '111-51',
          isSchedule: true,
          delay: 1,
          duration: 30,
          getNextCompletedQuestion: `function(color) {
            switch (color) {
              case '#25BA60':
                return '111-64';
              case '#FF8E1C':
                return '111-65';
              case '#E54D4D':
                return '111-66';
              default:
                return '111-64'; 
            }
          }`,
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-51-1',
                nextQuestionExternalId: '111-55',
              },
            },
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-51-2',
                nextQuestionExternalId: '111-55',
              },
            },
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-51-3',
                nextQuestionExternalId: '111-55',
              },
            },
          ],
          options: [
            {
              _id: '672c7dd33b9dda25d9b21493',
              value: 'Yes',
              externalId: '111-51-1',
            },
            {
              _id: '672c7ddb27d5cf9236b9cd96',
              value: 'No',
              externalId: '111-51-2',
            },
            {
              _id: '67452c4c88fe4b0d97fc42ca',
              value: "I don't know",
              carePartnerValue: "I don't know",
              externalId: '111-51-3',
            },
          ],
        },

        // Anually 40th day.
        // {
        //   _id: '67df852cf97c63a38940180c',
        //   question: 'Have you had Covid?',
        //   carePartnerText: 'Have you had Covid?',
        //   externalId: '111-69',
        //   isSchedule: true,
        //   delay: 40,
        //   duration: 1,
        //   recurringQuestion: true,
        //   actions: [
        //     {
        //       actionType: 'NextQuestion',
        //       event: 'OptionSelect',
        //       metadata: {
        //         optionExternalId: '*',
        //         nextQuestionExternalId: '111-70',
        //       },
        //     },
        //   ],
        //   options: [
        //     {
        //       _id: '67df8536d5a7e7fa7e2bbc08',
        //       value: 'Yes',
        //       externalId: '111-69-1',
        //     },
        //     {
        //       _id: '67df8540c587e88ea64c774f',
        //       value: 'No',
        //       externalId: '111-69-2',
        //     },
        //     {
        //       _id: '67df854931d5e096e68c5a5a',
        //       value: "I don't know",
        //       carePartnerValue: "I don't know",
        //       externalId: '111-69-3',
        //     },
        //   ],
        // },
        // {
        //   _id: '67df86717647385e2e461cc9',
        //   question: 'Would you like to monitor peak flow?  ',
        //   carePartnerText: 'Would you like to monitor peak flow?  ',
        //   externalId: '111-70',
        //   actions: [
        //     {
        //       actionType: 'NextQuestion',
        //       event: 'OptionSelect',
        //       metadata: {
        //         optionExternalId: '*',
        //         nextQuestionExternalId: '111-55',
        //       },
        //     },
        //   ],
        //   getNextCompletedQuestion: `function(color) {
        //     switch (color) {
        //       case '#25BA60':
        //         return '111-64';
        //       case '#FF8E1C':
        //         return '111-65';
        //       case '#E54D4D':
        //         return '111-66';
        //       default:
        //         return '111-64'; 
        //     }
        //   }`,
        //   options: [
        //     {
        //       _id: '67df867f1076cebd9b6056cf',
        //       value: 'Yes, and I have a peak flow meter',
        //       carePartnerValue: "Yes, and we have a peak flow meter",
        //       externalId: '111-69-1',
        //     },
        //     {
        //       _id: '67df8686d2bd6d3f6ec71d33',
        //       value: 'Yes, and I need a complimentary peak flow meter',
        //       carePartnerValue: "Yes, and I need a complimentary peak flow meter",
        //       externalId: '111-69-2',
        //     },
        //     {
        //       _id: '67df868c3ddf614049586de4',
        //       value: "No",
        //       carePartnerValue: "No",
        //       externalId: '111-69-3',
        //     },
        //     {
        //       _id: '67df8694602c4af9adcbc6ee',
        //       value: "I don't know",
        //       carePartnerValue: " I don't know",
        //       externalId: '111-69-4',
        //     },
        //   ],
        // },


        {
          question: '',
          type: 'Completed',
          subText: "That's great! Take your prescribed control and maintenance medicine as directed.",
          isDeleted: false,
          isOptional: false,
          externalId: '111-64',
          _id: '673c5bdaa409f56ccd307216',
          actions: [],
          options: [],
        },
        {
          question: '',
          type: 'Completed',
          subText: 'Darn it. Take your meds per action plan and keep your phone close - in case you need to call your provider.',
          isDeleted: false,
          isOptional: false,
          externalId: '111-65',
          _id: '673c5c47436ee33251029d5e',
          actions: [],
          options: [],
        },
        {
          question: '',
          type: 'Completed',
          subText: 'Alert. This app can wait. Seek help.',
          isDeleted: false,
          isOptional: false,
          externalId: '111-66',
          _id: '673c5c4fc0ddb7334a5e58da',
          actions: [],
          options: [],
        },
      ],
    },
  },
)
  .then(() => console.log('Created'))
  .catch(console.error);

// Multi topics test
TopicModel.updateOne(
  { _id: '67576a23fbc881413c712b75' },
  {
    $set: {
      name: 'Asthma - 2',
      nickname: 'Asthma - 2',
      category: 'Respiratory',
      externalId: '111',
      tags: ['Asthma - 2'],
      questions: [
        {
          _id: '672b9164a1b35aa1fc17b785',
          question: 'How are you connected to asthma?',
          externalId: '111-12',
          type: 'select',
          options: [
            {
              _id: '672b91a716b2ad7f5b749775',
              value: 'I have asthma',
              prompt: 'Got it!  Let’s make managing it easier together!',
              externalId: '111-12-1',
            },
            {
              _id: '672b917533d5cc5cc4a73360',
              value: 'I am supporting someone with asthma',
              prompt: 'Amazing!  I’m designed to help you support them at every step.',
              externalId: '111-12-2',
            },
            {
              _id: '67856c2dfe3d9a31bd64c031',
              value: 'I am a Healthcare Professional',
              prompt: 'Great!  Let’s get to work to empower your patients.',
              externalId: '111-12-3',
            },
          ],
        },
        // {
        //   _id: '672b8b8ffa5431b4c52d4fc2',
        //   question: 'What best descibes you?',
        //   externalId: '111-11',
        //   type: 'select',
        //   options: [
        //     {
        //       _id: '672b8eff66821551c1df511d',
        //       value: "I have asthma and I'm currently being treated",
        //       externalId: '111-11-1',
        //     },

        //     {
        //       _id: '672b8f3ef71d437737e100c2',
        //       value: 'I am a care partner for an adult with asthma',
        //       externalId: '111-11-2',
        //     },

        //     {
        //       _id: '672b8fb3d22680bded087b89',
        //       value: 'I am a care partner of a minor with asthma',
        //       externalId: '111-11-3',
        //     },
        //   ],
        // },
        // {
        //   _id: '672b9164a1b35aa1fc17b785',
        //   question: 'Are you or the person you are caring for currently being treated for asthma?',
        //   externalId: '111-12',
        //   type: 'select',
        //   options: [
        //     {
        //       _id: '672b91a716b2ad7f5b749775',
        //       value: 'Yes',
        //       externalId: '111-12-1',
        //     },
        //     {
        //       _id: '672b917533d5cc5cc4a73360',
        //       value: 'No',
        //       externalId: '111-12-2',
        //     },
        //   ],
        // },
        // {
        //   _id: '672b92461f275f797b4a305c',
        //   question: 'What are you hoping to accomplish?',
        //   externalId: '111-13',
        //   type: 'MultiSelect',
        //   options: [
        //     {
        //       _id: '672b92559d147489160ad2cc',
        //       value: 'I want to manage asthma better',
        //       externalId: '111-13-1',
        //     },
        //     {
        //       _id: '672b9263056fe588ebc218ec',
        //       value: 'I want to blogs more about asthma',
        //       externalId: '111-13-2',
        //     },
        //     {
        //       _id: '672b9270609c64b40e98142f',
        //       value: 'I want to connect with others (find my Rabble)',
        //       externalId: '111-13-3',
        //     },
        //   ],
        // },
      ],
      learningQuestions:  [
        {
          _id: "67979d8eccb0470b5551ab3a",
          question: "Learn about tracking asthma zones",
          type: "select",
          externalId: "111-21",
          options: [
            {
              _id: "67979df5d9127e2de2ebf4d9",
              value: "Green Zone 😊",
              description: `What is the Green Zone for Asthma?
      The Green Zone means your asthma is doing great. You can breathe easily, and you're not having any trouble with your chest, coughing, or wheezing.
      Here's what it looks like when you're in the Green Zone:
          • You can run and play without coughing or\n      getting tired too fast
          • You're not waking up at night because of\n      your asthma
          • You don't need your rescue inhaler (the one\n      that helps you breathe during an attack)
          • Your breathing feels normal and comfortable
      When you're in the Green Zone, you:
          • Take your daily asthma medicine (the controller)\n      just like your doctor says—even if you feel good!
          • Keep doing your normal activities, like school,\n      sports, and playing with friends
      Think of it like a traffic light:
          • 🟢 Green = Go! Everything is good
          • 🟡 Yellow = Slow down. Pay attention to your asthma
          • 🔴 Red = Stop! You need help right away
      Staying in the Green Zone is the goal! It means your asthma is under control and you're feeling your best.`,
              externalId: "111-21-1",
              color: "#25BA60"
            },
            {
              _id: "67979df5d9127e2de2ebf4da",
              value: "Yellow Zone 😐",
              description: `The Yellow Zone means your asthma is acting up a little. You might not feel your best, and breathing is a bit harder than usual. It's a warning to slow down and take action so things don't get worse.
      Here's what it looks like when you're in the Yellow Zone:
          • You're coughing, wheezing, or your chest\n      feels tight
          • You're having a harder time breathing than\n      normal
          • You're waking up at night because of asthma
          • You can't keep up with playing, running, or\n      other activities like you usually do
          • You need to use your rescue inhaler (the\n      quick-relief one)
      When you're in the Yellow Zone, you:
          • Use your rescue inhaler just like your doctor\n      or asthma plan says
          • Let a grown-up know how you're feeling
          • Rest and avoid things that make your asthma\n      worse (like smoke, dust, or running too much)
      Remember the traffic light:
          • 🟢 Green = Go! You're feeling good
          • 🟡 Yellow = Slow down! Asthma is getting worse
          • 🔴 Red = Stop! Get help now
      The Yellow Zone is your body's way of saying, "Hey, I need a little help!" If you act early, you can get back to the Green Zone faster.`,
              externalId: "111-21-2",
              color: "#FF8E1C"
            },
            {
              _id: "67979df5d9127e2de2ebf4db",
              value: "Danger Zone 😟",
              description: `The Red Zone means asthma is very serious right now. Breathing is hard, and medicine may not be working. It's time to get help fast.
      Here are signs you're in the Red Zone:
          • You feel very short of breath, even when\n      resting
          • You are coughing a lot or your chest feels\n      tight
          • You are wheezing or making it hard to speak\n      clearly
          • You used your rescue inhaler, but it's not\n      helping
          • Your lips or fingernails may look blue or gray
      What to do:
          • Use your quick-relief inhaler right away
          • Tell someone or call for help
          • Call 911 or go to the emergency room if you\n      are not getting better quickly
      Think of a stoplight:
          • 🟢 Green = Everything is good
          • 🟡 Yellow = Asthma is starting to get worse
          • 🔴 Red = Danger. Get help now
      The Red Zone is an emergency. Act fast to stay safe.`,
              externalId: "111-21-3",
              color: "#E54D4D"
            }
          ]
        }
      ],
      dailyTrackerQuestions: [
        {
          _id: '672c9b088972d5962a2c44d8',
          question: 'How would you rate your asthma control over the last 4 weeks?',
          carePartnerText: "How would you rate the patient's asthma control over the last 4 weeks?",
          externalId: '111-54',
          isSchedule: true,
          delay: 28,
          duration: 0,
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '*',
                nextQuestionExternalId: '111-55',
              },
            },
          ],
          options: [
            {
              _id: '672c9b0c36a0db9dae5574f2',
              value: '1 😠',
              externalId: '111-54-1',
            },
            {
              _id: '672c9b151b9e66c1dd655fcc',
              value: '2',
              externalId: '111-54-2',
            },
            {
              _id: '67452ea483ef0462888dae34',
              value: '3 😐',
              externalId: '111-54-3',
            },
            {
              _id: '67452e9de0ca74eeb1fc5fa8',
              value: '4',
              externalId: '111-54-4',
            },
            {
              _id: '67452e954b3df74b870ae959',
              value: '5 😊',
              externalId: '111-54-5',
            },
          ],
        },
        {
          _id: '673ca3b31c1bd160fda8eec1',
          question: 'What type of provider do you see for your asthma?',
          type: 'MultiSelect',
          carePartnerText: 'What type of provider does the patient see for asthma?',
          externalId: '111-67',
          isSchedule: true,
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '*',
                nextQuestionExternalId: '111-51',
              },
            },
          ],
          options: [
            {
              _id: '673ca2cf86965d05571faaa0',
              value: 'Primary Care/Family Practice',
              externalId: '111-67-1',
            },
            {
              _id: '673ca2d94e5fe27a02a7f9be',
              value: 'Pediatrician',
              externalId: '111-67-2',
            },
            {
              _id: '673ca2e1c3b30cfa64167f50',
              value: 'Pulmonologist',
              externalId: '111-67-3',
            },
            {
              _id: '673ca2ed639c46c8a3d63965',
              value: 'Urgent Care provider',
              externalId: '111-67-4',
            },
            {
              _id: '673ca2f660cee1c5600b0e9e',
              value: 'Allergist',
              externalId: '111-67-5',
            },
            {
              _id: '673ca2fd30de6c266983e920',
              value: 'Immunnologist',
              externalId: '111-67-6',
            },
            {
              _id: '673ca304156003b856a64f1e',
              value: 'Emergency room providers',
              externalId: '111-67-7',
            },
            {
              _id: '673ca30add91c5105ee044c1',
              value: 'Rheumotologist',
              externalId: '111-67-8',
            },
            {
              _id: '673ca3114937df729a8e2bfb',
              value: 'Ear, Nose, Throat',
              externalId: '111-67-9',
            },
            {
              _id: '673ca31fc48a1fe943f49167',
              value: 'Gastroentorologist',
              externalId: '111-67-10',
            },
          ],
        },

        {
          _id: '672c4b677fe069471a1a9ee1',
          question: 'How is your asthma today?',
          carePartnerText: 'How is their asthma today?',
          externalId: '111-55',
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-55-1',
                nextQuestionExternalId: '111-64',
              },
            },
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-55-2',
                nextQuestionExternalId: '111-56',
              },
            },
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-55-3',
                nextQuestionExternalId: '111-68',
              },
            },
          ],
          options: [
            {
              _id: '672c4bfe65e70fb062ec4534',
              value: 'Go Zone 🙂 - 2',
              externalId: '111-55-1',
              color: '#25BA60',
            },
            {
              _id: '672c4c86fecfa0affe1e8876',
              value: 'Caution Zone 🫤 -2',
              externalId: '111-55-2',
              color: '#FF8E1C',
            },
            {
              _id: '672c4c8e3f5ab9a1d13f964c',
              value: 'Danger Zone 😔 -2',
              externalId: '111-55-3',
              color: '#E54D4D',
            },
          ],
        },

        {
          _id: '672c4f5630ce98a691e5e3af',
          question: 'What symptoms are you experiencing?',
          carePartnerText: 'What symptoms is the patient experiencing?',
          type: 'MultiSelect',
          externalId: '111-56',
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '*',
                nextQuestionExternalId: '111-57',
              },
            },
          ],
          options: [
            {
              _id: '672c4f651e759289b080f1cb',
              value: 'None',
              externalId: '111-56-0',
            },
            {
              _id: '672c4f651e759289b080f1cb',
              value: 'Wheeze',
              externalId: '111-56-1',
            },
            {
              _id: '672c4f6c716a593c20921446',
              value: 'Cough',
              externalId: '111-56-2',
            },
            {
              _id: '672c4f7283a613e2a4afff03',
              value: 'Shortness of Breath',
              externalId: '111-56-3',
            },
            {
              _id: '672c4f7796dd982066204fc4',
              value: 'Activity limitation',
              externalId: '111-56-4',
            },
            {
              _id: '672c4f7c4f85ee50d372024a',
              value: 'Waking up due to asthma',
              externalId: '111-56-5',
            },
            {
              _id: '672c4f8181b372f57341bf7d',
              value: 'Drop in peak flow',
              externalId: '111-56-6',
            },
            {
              _id: '672c4f867fcea635256dc18a',
              value: 'Increased use of rescue inhaler',
              externalId: '111-56-7',
            },
          ],
        },
        {
          _id: '672c51c461edf9a3af6306fb',
          question: 'Did you recognize any triggers?',
          carePartnerText: 'Did the patient recognize any triggers?',
          type: 'MultiSelect',
          externalId: '111-57',
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '*',
                nextQuestionExternalId: '111-58',
              },
            },
          ],
          options: [
            {
              _id: '672c51cb6f7c27dcebbbe34a',
              value: 'Weather',
              externalId: '111-57-1',
            },
            {
              _id: '672c51d01f22e25e80d97886',
              value: 'Medicine',
              externalId: '111-57-2',
            },
            {
              _id: '672c51d675d6d09ec3afce5d',
              value: 'Pollution',
              externalId: '111-57-3',
            },
            {
              _id: '672c51dc09970531de6cd05f',
              value: 'Emotion',
              externalId: '111-57-4',
            },
            {
              _id: '672c51e3ea5a7b59b3ad4bc9',
              value: 'Exercise',
              externalId: '111-57-5',
            },
            {
              _id: '672c51e89a01de458fb5bd78',
              value: 'Allergens',
              externalId: '111-57-6',
            },
            {
              _id: '672c51eeb6757c0ad4b9e71f',
              value: 'Illness',
              externalId: '111-57-7',
            },
          ],
        },
        {
          _id: '672c521a2e5a541010b1445e',
          question: 'Where were you?',
          carePartnerText: 'Where was the patient?',
          externalId: '111-58',
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '*',
                nextQuestionExternalId: '111-59',
              },
            },
          ],
          options: [
            {
              _id: '672c522061633362fbbf7967',
              value: 'Home',
              externalId: '111-58-1',
            },
            {
              _id: '672c5226e9d639b0ead69466',
              value: 'Work',
              externalId: '111-58-2',
            },
            {
              _id: '672c522c5e54465861791fa2',
              value: 'School',
              externalId: '111-58-3',
            },
            {
              _id: '672c523a6a0357e14cea9df4',
              value: 'Outside',
              externalId: '111-58-4',
            },
            {
              _id: '672c52426a570ba3bef86e2c',
              value: 'Other',
              externalId: '111-58-5',
            },
          ],
        },
        {
          _id: '672c54f2d5e0c223c5a4abb0',
          question: 'Did you have an unplanned visit for care?',
          carePartnerText: 'Did the patient have an unplanned visit for care?',
          externalId: '111-59',
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '*',
                nextQuestionExternalId: '111-60',
              },
            },
          ],
          options: [
            {
              _id: '672c54bdc5af0c287ac4c78a',
              value: 'Yes',
              externalId: '111-59-1',
            },
            {
              _id: '672c54c3bfb92de2f1bce672',
              value: 'No',
              externalId: '111-59-2',
            },
          ],
        },
        {
          _id: '672c5528094af7a559e1ce26',
          question: 'Did you miss work or school?',
          externalId: '111-60',
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '*',
                nextQuestionExternalId: '111-61',
              },
            },
          ],
          options: [
            {
              _id: '672c553577580f67ac504ee6',
              value: 'Yes',
              externalId: '111-60-1',
            },
            {
              _id: '672c55399f3f07f104a59a44',
              value: 'No',
              externalId: '111-60-2',
            },
          ],
        },
        {
          _id: '672c56c9faabacff8ca4ce6f',
          question: 'Did you change your meds?',
          carePartnerText: 'Did the patient change their meds?',
          type: 'MultiSelect',
          externalId: '111-61',
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '*',
                nextQuestionExternalId: '111-52',
              },
            },
          ],
          options: [
            {
              _id: '672c56ecae9d60d939fa7913',
              value: 'No',
              externalId: '111-61-6',
            },
            {
              _id: '672c56ce8be73ae1618a3f70',
              value: 'Added an inhaler',
              externalId: '111-61-1',
            },
            {
              _id: '672c56d5d09742007b35423e',
              value: 'Nebulized',
              externalId: '111-61-2',
            },
            {
              _id: '672c56daee118cc02ade0251',
              value: 'Started an inhaled corticosteroid',
              externalId: '111-61-3',
            },
            {
              _id: '672c56e0250a9a40e1d29f89',
              value: 'Started a round of oral steroids',
              externalId: '111-61-4',
            },
            {
              _id: '672c56e7649ddbd4ce9947b3',
              value: 'Added Biologics',
              externalId: '111-61-5',
            },
          ],
        },
        {
          _id: '672c5890b67d956bf7a89334',
          question: 'Check all that apply',
          carePartnerText: 'Check all that apply',
          type: 'MultiSelect',
          externalId: '111-62',
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '*',
                nextQuestionExternalId: '111-52',
              },
            },
          ],
          options: [
            {
              _id: '672c58995946b69988de433f',
              value: 'I went to the emergency room',
              carePartnerValue: 'The patient went to the emergency room',
              externalId: '111-62-1',
            },
            {
              _id: '672c58a00fca4d03d2500eb9',
              value: 'I was admitted to the hospital',
              carePartnerValue: 'The patient was admitted to the hospital',
              externalId: '111-62-2',
            },
            {
              _id: '672c58a47df1e24d1e5d74bb',
              value: 'I took oral or iv steroids',
              carePartnerValue: 'The patient took oral or iv steroids',
              externalId: '111-62-3',
            },
            {
              _id: '672c58a47df1e24d1e5d74bb',
              value: 'I had a change in my inhaler',
              carePartnerValue: 'The patient had a change in inhaler',
              externalId: '111-62-6',
            },
            {
              _id: '672c58a47df1e24d1e5d74bb',
              value: 'I had a change in antibiotics',
              carePartnerValue: 'I had a change in antibiotics',
              externalId: '111-62-7',
            },
            {
              _id: '672c58a968f3582218c62bc6',
              value: 'A respiratory therapist helped my with my medicine',
              carePartnerValue: 'A respiratory therapist helped the patient with medicine',
              externalId: '111-62-4',
            },
            {
              _id: '672c58ae6d2a2c0bbbb33bc0',
              value: 'Something else',
              externalId: '111-62-5',
            },
          ],
        },
        {
          _id: '674749ce204ddbafe3b1807c',
          question: 'Dial 911 Emergency',
          carePartnerText: 'Dial 911 Emergency',
          externalId: '111-68',
          actions: [
            {
              actionType: 'Call',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-68-1',
                phone: '911',
              },
            },
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '*',
                nextQuestionExternalId: '111-62',
              },
            },
          ],
          options: [
            {
              _id: '674749dcb1a3269788c81380',
              value: 'Call 911',
              externalId: '111-68-1',
            },
            {
              _id: '674749e52feae78a30d451fe',
              value: "I'm Okay",
              carePartnerValue: "We're Okay",
              externalId: '111-68-2',
            },
          ],
        },

        {
          _id: '672c7eb9d2e36666d636e6ab',
          question: 'What severity of asthma do you have?',
          carePartnerText: 'What severity of asthma does the patient have?',
          externalId: '111-52',
          isSchedule: true,
          delay: 1,
          duration: 30,
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-52-1',
                nextQuestionExternalId: '111-53',
              },
            },
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-52-2',
                nextQuestionExternalId: '111-53',
              },
            },
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-52-3',
                nextQuestionExternalId: '111-53',
              },
            },
          ],
          options: [
            {
              _id: '672c7ec29c5b553cfc04beff',
              value: 'Mild',
              externalId: '111-52-1',
            },
            {
              _id: '672c7ecc340e6121becf4119',
              value: 'Moderate',
              externalId: '111-52-2',
            },
            {
              _id: '672c7ed4f992605d909aef1d',
              value: 'Severe',
              externalId: '111-52-2',
            },
          ],
        },
        {
          _id: '672c99e76c300a9c6eae79cf',
          question: 'What type of asthma do you have?',
          type: 'MultiSelect',
          carePartnerText: 'What type of asthma does the patient have?',
          externalId: '111-53',
          isSchedule: true,
          delay: 1,
          duration: 30,
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-53-1',
                nextQuestionExternalId: '111-67',
              },
            },
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-53-2',
                nextQuestionExternalId: '111-67',
              },
            },
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-53-3',
                nextQuestionExternalId: '111-67',
              },
            },
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-53-4',
                nextQuestionExternalId: '111-67',
              },
            },
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-53-5',
                nextQuestionExternalId: '111-67',
              },
            },
          ],
          options: [
            {
              _id: '672c98a001a02c3c6cdc848e',
              value: 'Allergic asthma (sometimes called rhinitis or Type 2 inflamation)',
              externalId: '111-53-1',
            },
            {
              _id: '672c98a94fee638c2a54a2f0',
              value: 'Non-allergic',
              externalId: '111-53-2',
            },
            {
              _id: '672c98b019980718babeb438',
              value: 'Exercised induced',
              externalId: '111-53-3',
            },
            {
              _id: '672c98b63d0d0b7e2f86c4a6',
              value: 'Nighttime asthma',
              externalId: '111-53-4',
            },
            {
              _id: '672c98bd044623b006d6a7ae',
              value: 'Eosinophilic asthma (sometimes called type 2 inflammation)',
              externalId: '111-53-5',
            },
            {
              _id: '672c98c4999110def75ab60e',
              value: "Don't know",
              externalId: '111-53-6',
            },
          ],
        },
        {
          _id: '672c7de0d587f6487df2ee89',
          question: 'Do you have an asthma action plan?',
          carePartnerText: 'Does the patient have an asthma action plan?',
          externalId: '111-51',
          isSchedule: true,
          delay: 1,
          duration: 30,
          getNextCompletedQuestion: `function(color) {
            switch (color) {
              case '#25BA60':
                return '111-64';
              case '#FF8E1C':
                return '111-65';
              case '#E54D4D':
                return '111-66';
              default:
                return '111-64'; 
            }
          }`,
          actions: [
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-51-1',
                nextQuestionExternalId: '111-55',
              },
            },
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-51-2',
                nextQuestionExternalId: '111-55',
              },
            },
            {
              actionType: 'NextQuestion',
              event: 'OptionSelect',
              metadata: {
                optionExternalId: '111-51-3',
                nextQuestionExternalId: '111-55',
              },
            },
          ],
          options: [
            {
              _id: '672c7dd33b9dda25d9b21493',
              value: 'Yes',
              externalId: '111-51-1',
            },
            {
              _id: '672c7ddb27d5cf9236b9cd96',
              value: 'No',
              externalId: '111-51-2',
            },
            {
              _id: '67452c4c88fe4b0d97fc42ca',
              value: "I don't know",
              carePartnerValue: "I don't know",
              externalId: '111-51-3',
            },
          ],
        },

        {
          question: '',
          type: 'Completed',
          subText: "That's great! Take your prescribed control and maintenance medicine as directed.",
          isDeleted: false,
          isOptional: false,
          externalId: '111-64',
          _id: '673c5bdaa409f56ccd307216',
          actions: [],
          options: [],
        },
        {
          question: '',
          type: 'Completed',
          subText: 'Darn it. Take your meds per action plan and keep your phone close - in case you need to call your provider.',
          isDeleted: false,
          isOptional: false,
          externalId: '111-65',
          _id: '673c5c47436ee33251029d5e',
          actions: [],
          options: [],
        },
        {
          question: '',
          type: 'Completed',
          subText: 'Alert. This app can wait. Seek help.',
          isDeleted: false,
          isOptional: false,
          externalId: '111-66',
          _id: '673c5c4fc0ddb7334a5e58da',
          actions: [],
          options: [],
        },
      ],
    },
  },
)
  .then(() => console.log('Created'))
  .catch(console.error);

  // Breast Cancer Topic
  TopicModel.updateOne(
    { _id: '67bf5a510589de0fde809a25' },
    {
      $set: {
        name: 'Breast Cancer',
        nickname: 'Breast Cancer',
        category: 'Cancer',
        externalId: '111',
        tags: ['Breast Cancer'],
        questions: [
          {
            _id: '672b8b8ffa5431b4c52d4fc2',
            question: 'How did you hear about myRabble?',
            externalId: '111-11',
            type: 'select',
            options: [
              {
                _id: '672b8eff66821551c1df511d',
                value: 'Doctor',
                externalId: '111-11-1',
              },
  
              {
                _id: '672b8f3ef71d437737e100c2',
                value: 'Family or Friends',
                externalId: '111-11-2',
              },
  
              {
                _id: '672b8fb3d22680bded087b89',
                value: 'Other Patients or Care Partners',
                externalId: '111-11-3',
              },
              {
                _id: '67856aad06e15aeee701dd12',
                value: 'Google Search',
                externalId: '111-11-4',
              },
              {
                _id: '67856af7a67b6d4f32dd7a02',
                value: 'Instagram / Facebook / Threads',
                externalId: '111-11-5',
              },
              {
                _id: '67856afe7e18a20e93de4085',
                value: 'News Article / Blog',
                externalId: '111-11-6',
              },
              {
                _id: '67856b09633349b924bb660f',
                value: 'Other',
                externalId: '111-11-7',
              },
            ],
          },
          {
            _id: '672b9164a1b35aa1fc17b785',
            question: 'Why are you focused on breast cancer?',
            externalId: '111-12',
            type: 'select',
            options: [
              {
                _id: '672b91a716b2ad7f5b749775',
                value: 'to manage my breast cancer better',
                prompt: "Got it!  Let's make managing it easier together!",
                externalId: '111-12-1',
              },
              {
                _id: '672b917533d5cc5cc4a73360',
                value: 'to support someone facing breast cancer',
                prompt: "Amazing!  I'm designed to help you support them at every step.",
                externalId: '111-12-2',
              },
              {
                _id: '67856c2dfe3d9a31bd64c031',
                value: 'I am a Healthcare Provider',
                prompt: "Great!  Let's get to work to empower your patients.",
                externalId: '111-12-3',
              },
            ],
          },
          {
            _id: '672b92461f275f797b4a305c',
            question: 'What aspect of breast cancer is most relevant to you?',
            externalId: '111-13',
            type: 'select',
            options: [
              {
                _id: '672b92559d147489160ad2cc',
                value: 'New Diagnosis',
                prompt: "Sounds good, we'll start from the beginning.",
                externalId: '111-13-1',
              },
              {
                _id: '672b9263056fe588ebc218ec',
                value: 'Active Treatment for early stage cancer',
                prompt: 'Ok! We’ll build on what you know!',
                externalId: '111-13-2',
              },
              {
                _id: '672b9270609c64b40e98142f',
                value: 'Active Treatment for metastatic cancer',
                prompt: "Wow, that’s great! If you have ideas to make it better for others, we'd love to listen.",
                externalId: '111-13-3',
              },
              {
                _id: '67856e0ea97e6cecb12ee87b',
                value: 'Survivorship',
                prompt: 'Wow! That’s great!',
                externalId: '111-13-4',
              },
            ],
          },
          {
            _id: '67bffda0fe749c1f238d977a',
            question: 'Would you like to log your treatment plan daily?',
            externalId: '111-15',
            type: 'MultiSelect',
            options: [
              {
                _id: '67bffdab9eeba6f4cc291c28',
                value: 'Yes',
                externalId: '111-15-1',
              },
              {
                _id: '67bffdbf4aeb01582848fa67',
                value: 'No',
                externalId: '111-15-2',
              },
            ],
          },
          {
            _id: '67856f299cc16fcc9db0ea21',
            question: 'What should be our goals?',
            externalId: '111-14',
            type: 'MultiSelect',
            options: [
              {
                _id: '67856f3109f04fe0a46a5330',
                value: 'to manage breast cancer',
                externalId: '111-14-1',
              },
              {
                _id: '67856f39fa2ca8d432415092',
                value: 'to understand my diagnosis and treatment options',
                externalId: '111-14-2',
              },
              {
                _id: '67856f42435772caae2c22e0',
                value: 'to connect with others',
                externalId: '111-14-3',
              },
              {
                _id: '67856f481179ac8948b13f3a',
                value: 'to access services',
                externalId: '111-14-4',
              },
            ],
          },
        ],
        learningQuestions: [
          {
            _id: '67979d8eccb0470b5551ab3a',
            question: 'How do we track today’s symptoms?',
            externalId: '111-21',
            type: 'select',
            options: [
              {
                _id: '67979df5d9127e2de2ebf4d9',
                value: 'Asthma Zones',
                externalId: '111-21-1',
                description: [
                  { color: 'green', label: 'Green Zone 😊' },
                  { color: 'yellow', label: 'Yellow Zone 😐' },
                  { color: 'red', label: 'Danger Zone 😟' },
                ],
              },
              {
                _id: '67979dff8d8172f70ee94029',
                value: 'Asthma Severity',
                description: 'symptom frequency',
                externalId: '111-21-2',
              },
            ],
          },
          {
            _id: '67979fc46e591c11d4d01a62',
            question: 'Which of these might describe someone facing severe asthma?',
            externalId: '111-22',
            type: 'MultiSelect',
            options: [
              {
                _id: '6797aa7bc4609754acd034b5',
                value: 'symptoms throughout the day',
                externalId: '111-22-1',
              },
              {
                _id: '6797aa827b8eaa4dc40bade0',
                value: 'frequent nighttime awakenings',
                externalId: '111-22-2',
              },
              {
                _id: '6797aa8c0764a9c1d3879e5c',
                value: '2 or more exacerbations per year',
                externalId: '111-22-3',
              },
              {
                _id: '6797aa953dd58b7edadc77c8',
                value: 'unable to perform daily activities',
                externalId: '111-22-4',
              },
            ],
          },
        ],
        dailyTrackerQuestions: [
  
          {
            _id: '672c4b677fe069471a1a9ee1',
            question: 'How do you feel emotionally today?',
            carePartnerText: 'How do they feel emotionally today?',
            externalId: '111-55',
            actions: [
              {
                actionType: 'NextQuestion',
                event: 'OptionSelect',
                metadata: {
                  optionExternalId: '111-55-1',
                  nextQuestionExternalId: '111-60',
                },
              },
              {
                actionType: 'NextQuestion',
                event: 'OptionSelect',
                metadata: {
                  optionExternalId: '111-55-2',
                  nextQuestionExternalId: '111-60',
                },
              },
              {
                actionType: 'NextQuestion',
                event: 'OptionSelect',
                metadata: {
                  optionExternalId: '111-55-3',
                  nextQuestionExternalId: '111-60',
                },
              },
            ],
            options: [
              {
                _id: '672c4bfe65e70fb062ec4534',
                value: 'Good 🙂',
                externalId: '111-55-1',
                color: '#25BA60',
              },
              {
                _id: '672c4c86fecfa0affe1e8876',
                value: 'Okay 🫤',
                externalId: '111-55-2',
                color: '#FFBE1C',
              },
              {
                _id: '672c4c8e3f5ab9a1d13f964c',
                value: 'Not Great 😔',
                externalId: '111-55-3',
                color: '#E54D4D',
              },
            ],
          },
  
          {
            _id: '672c4f5630ce98a691e5e3af',
            question: 'What type of treatment would you like to log today?',
            carePartnerText: 'What type of treatment would you like to log today?',
            type: 'MultiSelect',
            externalId: '111-56',
            actions: [
              {
                actionType: 'NextQuestion',
                event: 'OptionSelect',
                metadata: {
                  optionExternalId: '*',
                  nextQuestionExternalId: '111-57',
                },
              },
            ],
            options: [
              {
                _id: '672c4f651e759289b080f1cb',
                value: 'Radiation',
                externalId: '111-56-1',
              },
              {
                _id: '672c4f6c716a593c20921446',
                value: 'Infusion',
                externalId: '111-56-2',
              },
              {
                _id: '672c4f7283a613e2a4afff03',
                value: 'Oral Medication',
                externalId: '111-56-3',
              },
              {
                _id: '672c4f7796dd982066204fc4',
                value: 'Surgery',
                externalId: '111-56-4',
              },
              {
                _id: '672c4f7c4f85ee50d372024a',
                value: 'Notes: (free text)',
                externalId: '111-56-5',
              },
            
            ],
          },
          {
            _id: '672c51c461edf9a3af6306fb',
            question: 'Would you like information related to available services?',
            carePartnerText: 'Would you like information related to available services?',
            type: 'select',
            externalId: '111-57',
            actions: [
              {
                actionType: 'NextQuestion',
                event: 'OptionSelect',
                metadata: {
                  optionExternalId: '*',
                  nextQuestionExternalId: '111-58',
                },
              },
            ],
            options: [
              {
                _id: '672c51cb6f7c27dcebbbe34a',
                value: 'Yes',
                externalId: '111-57-1',
              },
              {
                _id: '672c51d01f22e25e80d97886',
                value: 'No',
                externalId: '111-57-2',
              },
            ],
          },
          {
            _id: '672c521a2e5a541010b1445e',
            question: 'Would you like to participate with a group of people who have a similar diagnosis?',
            carePartnerText: 'Would you like to participate with a group of people who have a similar diagnosis?',
            externalId: '111-58',
            actions: [
              {
                actionType: 'NextQuestion',
                event: 'OptionSelect',
                metadata: {
                  optionExternalId: '*',
                  nextQuestionExternalId: '111-59',
                },
              },
            ],
            options: [
              {
                _id: '672c522061633362fbbf7967',
                value: 'Yes',
                externalId: '111-58-1',
              },
              {
                _id: '672c5226e9d639b0ead69466',
                value: 'No',
                externalId: '111-58-2',
              },
            ],
          },
          {
            _id: '672c54f2d5e0c223c5a4abb0',
            question: "We're developing a peer mentor program, would you be interested in sharing your thoughts?",
            carePartnerText: "We're developing a peer mentor program, would you be interested in sharing your thoughts?",
            externalId: '111-59',
            actions: [
              {
                actionType: 'NextQuestion',
                event: 'OptionSelect',
                metadata: {
                  optionExternalId: '*',
                  nextQuestionExternalId: '111-68',
                },
              },
            ],
            options: [
              {
                _id: '672c54bdc5af0c287ac4c78a',
                value: 'Yes',
                externalId: '111-59-1',
              },
              {
                _id: '672c54c3bfb92de2f1bce672',
                value: 'No',
                externalId: '111-59-2',
              },
            ],
          },

          {
            _id: '67dc3751e9ae0c46bfc78222',
            question: "We're developing a program around research participation, would you be interested in sharing your thoughts?",
            carePartnerText: "We're developing a program around research participation, would you be interested in sharing your thoughts?",
            externalId: '111-68',
            actions: [
              {
                actionType: 'NextQuestion',
                event: 'OptionSelect',
                metadata: {
                  optionExternalId: '*',
                  nextQuestionExternalId: '111-61',
                },
              },
            ],
            options: [
              {
                _id: '67dc375a10e1417f0f7bda67',
                value: 'Yes',
                externalId: '111-68-1',
              },
              {
                _id: '67dc3764d5a1ba02b194ff59',
                value: 'No',
                externalId: '111-68-2',
              },
            ],
          },

          {
            _id: '672c5528094af7a559e1ce26',
            question: 'How do you feel physically today?',
            carePartnerText: 'How do they feel physically today?',
            externalId: '111-60',
            actions: [
              {
                actionType: 'NextQuestion',
                event: 'OptionSelect',
                metadata: {
                  optionExternalId: '111-60-1',
                  nextQuestionExternalId: '111-61',
                },
              },
              {
                actionType: 'NextQuestion',
                event: 'OptionSelect',
                metadata: {
                  optionExternalId: '111-60-2',
                  nextQuestionExternalId: '111-57',
                },
              },
              {
                actionType: 'NextQuestion',
                event: 'OptionSelect',
                metadata: {
                  optionExternalId: '111-60-3',
                  nextQuestionExternalId: '111-61',
                },
              },
            ],
            options: [
              {
                _id: '672c553577580f67ac504ee6',
                value: 'Good 🙂',
                externalId: '111-60-1',
                color: '#25BA60',
              },
              {
                _id: '672c55399f3f07f104a59a44',
                value: 'Okay 🫤',
                externalId: '111-60-2',
                color: '#FFBE1C',
              },
                {
                _id: '67c00178512b204ed0d5be31',
                value: 'Not Great😔',
                externalId: '111-60-3',
                color: '#E54D4D',
              },
            ],
          },
          {
            _id: '672c56c9faabacff8ca4ce6f',
            question: 'Do any of these impact you today?  ',
            carePartnerText: 'Do any of these impact you today?',
            type: 'MultiSelect',
            externalId: '111-61',
            actions: [
              {
                actionType: 'NextQuestion',
                event: 'OptionSelect',
                metadata: {
                  optionExternalId: '*',
                  nextQuestionExternalId: '111-52',
                },
              },
            ],
            options: [
              {
                _id: '672c56ecae9d60d939fa7913',
                value: 'Bone pain or aching',
                externalId: '111-61-6',
              },
              {
                _id: '672c56ce8be73ae1618a3f70',
                value: 'Changes in nail color, texture, or strength',
                externalId: '111-61-1',
              },
              {
                _id: '672c56d5d09742007b35423e',
                value: 'Dry, itchy skin',
                externalId: '111-61-2',
              },
              {
                _id: '672c56daee118cc02ade0251',
                value: 'Dryness or discomfort in the vaginal area',
                externalId: '111-61-3',
              },
              {
                _id: '672c56e0250a9a40e1d29f89',
                value: 'Early menopause symptoms',
                externalId: '111-61-4',
              },
              {
                _id: '672c56e7649ddbd4ce9947b3',
                value: 'Feeling anxious or worried',
                externalId: '111-61-5',
              },
              {
                _id: '67c0038f964835ddb58e82fb',
                value: 'Feeling sad or down (depression)',
                externalId: '111-61-6',
              },
              {
                _id: '67c00386fbbbdfe4bf3a93b9',
                value: 'Feeling very tired or exhausted',
                externalId: '111-61-7',
              },
              {
                _id: '67c0037a4a6b6379c694745d',
                value: 'Gaining weight without trying',
                externalId: '111-61-8',
              },
              {
                _id: '67c00354c9c6273ec0e877b6',
                value: 'General pain or soreness',
                externalId: '111-61-9',
              },
              {
                _id: '67c0034e8dc1f4384c7d14c8',
                value: 'Getting sick more easily due to a weaker immune system',
                externalId: '111-61-10',
              },
              {
                _id: '67c00341e0833b6cd57c1b80',
                value: 'Hair thinning or hair loss',
                externalId: '111-61-11',
              },
              {
                _id: '67c0033a82848602695a34b3',
                value: 'Heart problems or changes from treatment',
                externalId: '111-61-12',
              },
              {
                _id: '67c00333519dcfe5c825ac72',
                value: 'Hot flashes and night sweats (like menopause)',
                externalId: '111-61-13',
              },
              {
                _id: '67c0032c1a3c291df56b451f',
                value: 'Increased risk of blood clots',
                externalId: '111-61-14',
              },
              {
                _id: '67c0032569b970241833da11',
                value: 'Loose stools or diarrhea',
                externalId: '111-61-15',
              },
              {
                _id: '672c56e7649ddbd4ce9947b3',
                value: 'Mood swings or feeling emotional',
                externalId: '111-61-16',
              },
              {
                _id: '67c0031ebaf5b5db78028d39',
                value: 'Not feeling hungry or losing your appetite',
                externalId: '111-61-17',
              },
              {
                _id: '67c003195e1401eda24fff93',
                value: 'Skin becoming more sensitive than usual',
                externalId: '111-61-18',
              },
              {
                _id: '67c0031299fa331b30a5bd99',
                value: 'Skin rash or irritation',
                externalId: '111-61-19',
              },
              {
                _id: '67c0030dd130c7089a0fc09b',
                value: 'Sudden feelings of warmth and sweating (hot flashes)',
                externalId: '111-61-20',
              },
              {
                _id: '67c003079692d61e2af9ab0a',
                value: 'Swelling in the arm or chest (lymphedema)',
                externalId: '111-61-21',
              },
              {
                _id: '67c003019a05f0452b0761b2',
                value: 'Throwing up (vomiting)',
                externalId: '111-61-22',
              },
              {
                _id: '67c002fb41b188cb4fa58cf2',
                value: 'Tingling, numbness, or burning in hands or feet (nerve damage)',
                externalId: '111-61-23',
              },
              {
                _id: '67c002f188f87d8abb36108a',
                value: 'Trouble getting pregnant or fertility concerns',
                externalId: '111-61-24',
              },
              {
                _id: '67c00484a2e9553665460294',
                value: 'Trouble going to the bathroom (constipation)',
                externalId: '111-61-25',
              },
              {
                _id: '67c00489a243ec65878cf562',
                value: 'Trouble remembering things or thinking clearly ("chemo brain")',
                externalId: '111-61-26',
              },
              {
                _id: '67c004906e7d07d063dc2137',
                value: 'Upset stomach or feeling like throwing up (nausea)',
                externalId: '111-61-27',
              },
              {
                _id: '67c00495aaabbec9af7d5002',
                value: 'Weaker bones or bone loss (osteoporosis)',
                externalId: '111-61-28',
              },
               {
                  _id: '67c0049c8a8c166d1f08c963',
                  value: 'Worrying that the cancer might come back',
                  externalId: '111-61-28',
                },
            ],
            getNextCompletedQuestion: `function(color) {
              switch (color) {
                case '#25BA60':
                  return '111-64';
                case '#FF8E1C':
                  return '111-65';
                case '#E54D4D':
                  return '111-66';
                default:
                  return '111-64'; 
              }
            }`
          },

          {
            question: '',
            type: 'Completed',
            subText: "That's great! Take your prescribed control and maintenance medicine as directed.",
            isDeleted: false,
            isOptional: false,
            externalId: '111-64',
            _id: '673c5bdaa409f56ccd307216',
            actions: [],
            options: [],
          },
          {
            question: '',
            type: 'Completed',
            subText: 'Darn it. Take your meds per action plan and keep your phone close - in case you need to call your provider.',
            isDeleted: false,
            isOptional: false,
            externalId: '111-65',
            _id: '673c5c47436ee33251029d5e',
            actions: [],
            options: [],
          },


          {
          question: '',
          type: 'Completed',
          subText: "That's great! Take your prescribed control and maintenance medicine as directed.",
          isDeleted: false,
          isOptional: false,
          externalId: '111-64',
          _id: '673c5bdaa409f56ccd307216',
          actions: [],
          options: [],
        },
        {
          question: '',
          type: 'Completed',
          subText: 'Darn it. Take your meds per action plan and keep your phone close - in case you need to call your provider.',
          isDeleted: false,
          isOptional: false,
          externalId: '111-65',
          _id: '673c5c47436ee33251029d5e',
          actions: [],
          options: [],
        },
        {
          question: '',
          type: 'Completed',
          subText: 'Alert. This app can wait. Seek help.',
          isDeleted: false,
          isOptional: false,
          externalId: '111-66',
          _id: '673c5c4fc0ddb7334a5e58da',
          actions: [],
          options: [],
        },
        ],
      },
    },
  )
  .then(() => console.log('Created'))
  .catch(console.error);
