import { z } from 'zod';
import { createCategory, createUpdateService, updateServiceSequence } from '../controller/service.controller';
import { ServiceCategoryModel, ServiceModel } from '../models/Service';
import t from '../startup/trpc';
import {
  createCategoryValidator,
  createOrUpdateServiceValidator,
  serviceFilterValidator,
  updateServiceSequenceValidator,
} from '../../../shared/validators/service.validator';
import { ServiceStatusEnum } from '../../../shared/types/service';

const serviceRoute = t.router({
  createCategory: t.procedure.input(createCategoryValidator).mutation(({ input }) => createCategory(input)),
  getCategories: t.procedure.query(() => ServiceCategoryModel.find().sort({ sequence: 1 }).lean()),
  createUpdateService: t.procedure.input(createOrUpdateServiceValidator).mutation(({ input }) => createUpdateService(input)),
  getService: t.procedure.input(serviceFilterValidator).query(({ input }) => {
    return ServiceModel.find({
      ...(input?.states?.length && { states: { $in: input.states } }),
      ...(input?.category && { category: input.category }),
      ...(input?.topics?.length && { topics: { $in: input.topics } }),
      status: input?.status?.length ? { $in: input?.status } : { $ne: ServiceStatusEnum.INACTIVE },
    })
      .sort({ sequence: 1 })
      .lean();
  }),

  getServiceById: t.procedure.input(z.string()).query(({ input }) => ServiceModel.findById(input).lean()),

  updateServiceSequence: t.procedure.input(updateServiceSequenceValidator).mutation(({ input }) => updateServiceSequence(input)),
});

export default serviceRoute;
