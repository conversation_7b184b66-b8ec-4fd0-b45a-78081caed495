import { authProcedure } from '../middleware/auth';
import t from '../startup/trpc';
import {
  leaveGroupValidator,
  requestInviteValidator,
  getUsersInGroupValidator,
  createRabbleGroupValidator,
  deleteRabbleGroupValidator,
  searchRabbleGroupValidator,
  handleRabbleInviteValidator,
  promoteDemoteRabbleUserValidator,
  addUserToRabbleGroupValidator,
  getGroupInfoValidator,
  getRabbleInvitesValidator,
  kickUserValidator,
  updateRabbleGroupPartialsValidator,
  getDiscoverGroupsValidator,
  getRabbleInvitesWithEmailValidator,
} from '../../../shared/validators/rabblegroup.validator';
import {
  addUserToGroup,
  caregiverPatientJoinedGroups,
  createRabbleGroup,
  deleteRabbleGroup,
  getGroupInfo,
  getRabbleInvites,
  getRabbleInvitesWithEmail,
  getUserCreatedGroups,
  getUsersInGroup,
  handleRabbleInvite,
  inviteSignupRabble,
  joinPublicRabbleGroup,
  kickUser,
  leaveGroup,
  promoteOrDemoteUser,
  requestInvite,
  searchRabbleGroup,
  updateRabbleGroupPartials,
  userJoinedGroups,
} from '../controller/rabblegroup.controller';
import { RabbleGroupModel } from '../models/RabbleGroup';
import { FilterQuery } from 'mongoose';
import { RabbleGroup } from '../../../shared/types/RabbleGroup';

export const rabbleGroupRouter = t.router({
  addUserToGroup: authProcedure()
    .input(addUserToRabbleGroupValidator)
    .mutation(({ input }) => addUserToGroup(input)),
  joinPublicRabbleGroup: authProcedure()
    .input(addUserToRabbleGroupValidator)
    .mutation(({ input }) => joinPublicRabbleGroup(input)),
  promoteOrDemoteUser: authProcedure()
    .input(promoteDemoteRabbleUserValidator)
    .mutation(({ ctx, input }) => promoteOrDemoteUser(input, ctx.user._id)),
  getUsersInGroup: authProcedure()
    .input(getUsersInGroupValidator)
    .query(({ input }) => getUsersInGroup(input)),
  getGroupInfo: t.procedure.input(getGroupInfoValidator).query(({ input }) => getGroupInfo(input)),
  leaveGroup: authProcedure()
    .input(leaveGroupValidator)
    .mutation(({ ctx, input }) => leaveGroup(input, ctx.user._id)),
  kickUser: authProcedure()
    .input(kickUserValidator)
    .mutation(({ input }) => kickUser(input)),
  createRabbleGroup: authProcedure()
    .input(createRabbleGroupValidator)
    .mutation(({ ctx, input }) => createRabbleGroup(input, ctx.user._id)),
  updateRabbleGroupPatials: authProcedure()
    .input(updateRabbleGroupPartialsValidator)
    .mutation(({ ctx, input }) => updateRabbleGroupPartials(input, ctx.user._id)),
  searchRabbleGroup: t.procedure.input(searchRabbleGroupValidator).query(({ input }) => searchRabbleGroup(input)),
  getUserCreatedGroups: authProcedure().query(({ ctx }) => getUserCreatedGroups(ctx.user._id)),
  discoverRabbleGroups: t.procedure.input(getDiscoverGroupsValidator).query(({ input }) => {
    const filter: FilterQuery<RabbleGroup> = { deleted: false };
    if (input) input.diseaseTags?.length && (filter.diseaseTags = { $in: input?.diseaseTags });
    return RabbleGroupModel.find(filter).lean();
  }),
  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  userJoinedGroups: authProcedure().query(({ ctx }) => userJoinedGroups({ user: ctx.user._id! as string })),
  caregiverPatientJoinedGroups: authProcedure().query(({ ctx }) => caregiverPatientJoinedGroups(ctx.user._id)),
  deleteRabbleGroup: authProcedure()
    .input(deleteRabbleGroupValidator)
    .mutation(({ ctx, input }) => deleteRabbleGroup(input, ctx.user._id)),
  requestInvite: authProcedure()
    .input(requestInviteValidator)
    .mutation(({ input, ctx }) => requestInvite(input, ctx.user._id.toString())),
  handleRabbleInvite: authProcedure()
    .input(handleRabbleInviteValidator)
    .mutation(({ ctx, input }) => handleRabbleInvite(input, ctx.user._id)),
  getRabbleInvites: authProcedure()
    .input(getRabbleInvitesValidator)
    .query(({ input, ctx }) => getRabbleInvites(input, ctx.user._id.toString())),
  getRabbleInvitesWithEmail: authProcedure()
    .input(getRabbleInvitesWithEmailValidator)
    .query(({ input, ctx }) => getRabbleInvitesWithEmail(input)),
  inviteSignupRabble: authProcedure()
    .input(handleRabbleInviteValidator)
    .mutation(({ ctx, input }) => inviteSignupRabble(input, ctx.user._id)),
});
