import { z } from 'zod';
import { zString } from '../../../shared/validators';
import {
  createBlogTagValidator,
  manageBlogValidator,
  getBlogsValidator,
  manageBlogsDashboardValidator,
  getBlogValidator,
} from '../../../shared/validators/blog';
import { manageBlog, createBlogTag, getBlogTags, getBlogs, manageBlogDashboard, getBlog, deleteBlogTag } from '../controller/blog.controller';
import { authProcedure } from '../middleware/auth';
import { BlogDashboardModel, BlogModel } from '../models/Blog';
import t from '../startup/trpc';

export const blogRouter = t.router({
  getBlogTags: t.procedure.query(getBlogTags),
  createBlogTag: authProcedure(['SUPER_ADMIN', 'ADMIN', 'MODERATOR'])
    .input(createBlogTagValidator)
    .mutation(({ input }) => createBlogTag(input)),
  createBlog: authProcedure(['SUPER_ADMIN', 'ADMIN', 'MODERATOR'])
    .input(manageBlogValidator)
    .mutation(({ input }) => manageBlog(input)),
  getBlogs: t.procedure.input(getBlogsValidator).query(({ input }) => getBlogs(input)),
  deleteBlog: t.procedure.input(zString).mutation(({ input }) => BlogModel.findByIdAndDelete(input)),
  getBlog: t.procedure.input(getBlogValidator).query(({ input }) => getBlog(input)),
  manageBlogDashboard: authProcedure(['SUPER_ADMIN', 'ADMIN', 'MODERATOR'])
    .input(manageBlogsDashboardValidator)
    .mutation(({ input }) => manageBlogDashboard(input)),
  getBlogDashboard: t.procedure.query(() => BlogDashboardModel.findOne().lean()),
  deleteBlogTag: t.procedure.input(z.string()).mutation(({ input }) => deleteBlogTag(input)),
});
