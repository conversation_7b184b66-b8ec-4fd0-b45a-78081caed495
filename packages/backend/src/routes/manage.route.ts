import t from '../startup/trpc';
import { authProcedure } from '../middleware/auth';
import {
  getTopicList,
  getUserTopics,
  upsertUserTopic,
  getUserDailyTrackers,
  upsertUserDailyTracker,
  unsubscribeUserTopic,
  getBehaviourAnswerDays,
  getNextQuestion,
  getCalenderEvents,
  getCalenderWeeksAndColor,
  clearCache,
  getStreak,
  getProgressReportHtml,
} from '../controller/manage.controller';
import {
  getBehaviourAnswerDaysValidator,
  getCalenderEventsValidator,
  getNextQuestionValidator,
  getUserDailyTrackerValidator,
  getUserTopicsValidator,
  streakValidator,
  topicListValidator,
  unsubscribeUserTopicValidator,
  upsertUserDailyTrackerValidator,
  upsertUserTopicValidator,
} from '../../../shared/validators/manage.validator';

const manageRouter = t.router({
  
  getTopics: authProcedure()
    .input(topicListValidator)
    .query(({ input }) => getTopicList(input)),

  getUserTopics: authProcedure()
    .input(getUserTopicsValidator)
    .query(({ input, ctx }) => getUserTopics(input, ctx.user._id.toString())),

  upsertUserTopic: authProcedure()
    .input(upsertUserTopicValidator)
    .mutation(({ input, ctx }) => upsertUserTopic(input, ctx.user._id.toString())),

  getUserDailyTrackers: authProcedure()
    .input(getUserDailyTrackerValidator)
    .query(({ input, ctx }) => getUserDailyTrackers(input, ctx.user._id.toString())),

  upsertUserDailyTracker: authProcedure()
    .input(upsertUserDailyTrackerValidator)
    .mutation(({ input, ctx }) => upsertUserDailyTracker(input, ctx.user._id.toString())),

  getBehaviourAnswerDays: authProcedure()
    .input(getBehaviourAnswerDaysValidator)
    .query(({ input, ctx }) => getBehaviourAnswerDays(input, ctx.user._id.toString())),

  getNextQuestion: authProcedure()
    .input(getNextQuestionValidator)
    .mutation(({ input, ctx }) => getNextQuestion(input, ctx.user._id.toString())),

  getCalenderEvents: authProcedure()
    .input(getCalenderEventsValidator)
    .mutation(({ input, ctx }) => getCalenderEvents(input, ctx.user._id.toString())),

  getCalenderWeeksAndColor: authProcedure()
    .input(getCalenderEventsValidator)
    .mutation(({ input, ctx }) => getCalenderWeeksAndColor(input, ctx.user._id.toString())),

  unsubscribeUserTopic: authProcedure()
    .input(unsubscribeUserTopicValidator)
    .mutation(({ input, ctx }) => unsubscribeUserTopic(input, ctx.user._id.toString())),
  
  clearCache: t.procedure.query(() => clearCache()),

  getStreak: authProcedure()
    .input(streakValidator)
    .query(({ ctx, input }) => getStreak(ctx.user._id.toString(), input.topicId)),

  getProgressReport: authProcedure()
    .input(getCalenderEventsValidator)
    .mutation(({ input, ctx }) =>  getProgressReportHtml(input, ctx.user._id.toString())),
});

export default manageRouter;
