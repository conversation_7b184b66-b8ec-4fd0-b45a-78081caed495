/* eslint-disable func-names */
/* eslint-disable @typescript-eslint/ban-ts-comment */
import mongoose, { Schema, model } from 'mongoose';
import { Contact, ProfileAddress, UserAccount, UserProfile, UserProfilePersonalDetails } from '../../../shared/types/user';
import mixpanel, { MIXPANEL_TOKEN } from '../utils/mixpanel';
import { CaregiverApprovalStatus, CaregiverRelationTypes } from '../../../shared/enums/user';
import TopicModel from './Topics';

const managedBySchema = new Schema({
  user: { type: Schema.Types.ObjectId, ref: 'user-account' },
  accountType: { type: String, enum: ['caregiver'] },
  relationship: { type: String, enum: CaregiverRelationTypes },
  createdAt: { type: Date, default: new Date() },
  approvalStatus: { type: String, default: CaregiverApprovalStatus.APPROVED, enum: CaregiverApprovalStatus }, // 0 is pending approval
});

const schema = new Schema<UserAccount>(
  {
    contact: new Schema<Contact>({ countryCode: { type: String, default: process.env.COUNTRY_CODE }, phone: { type: String } }),
    email: { type: String },
    firstname: String,
    lastname: String,
    profilePicture: String,
    role: { required: true, type: String, default: 'USER' },
    username: String,
    isCaregiver: { required: false, type: Boolean, default: false },
    marketingConsent: Boolean,
    tncConsent: Boolean,
    privacyConsent: Boolean,
    deviceId: String,
    month: String,
    year: String,
    age: String,
    isOnboardingCompleted: { required: false, type: Boolean, default: false },
    lastLoggedIn: { required: false, type: Date },
    lastInactiveMailSent: { required: false, type: Date },
    lastMangeImpressionMade: [
      {
        topic: { type: mongoose.Schema.Types.ObjectId, ref: TopicModel.modelName },
        date: Date,
        isComplete: { type: Boolean, default: false },
        enableNotifications: { type: Boolean, default: true },
      },
    ],
    managedBy: { required: false, type: [managedBySchema], default: [] },
  },
  { timestamps: true },
);

schema.post('updateOne', async function () {
  const userId = this.getFilter()._id;
  const user = await this.model.findById(userId);
  if (user) {
    const userProfile = await model('user-profile').findOne({ user: userId });
    /**
     * 
     *  address: {
    city: '1111111',
    state: 'Arizona',
    zip: '11111',
    _id: new ObjectId("66140ad9f8cf971ea9be7e2d")
  },
  personal: {
    dob: 1992-04-08T15:01:28.074Z,
    gender: 'male',
    race: 'American Indian or Alaska Native',
    _id: new ObjectId("66140ad9f8cf971ea9be7e2c")
  }
     */
    await mixpanel.createUserProfile({
      // @ts-ignore
      $distinct_id: String(user._id),
      // @ts-ignore
      $set: {
        // @ts-ignore
        first_name: user.firstname,
        last_name: user.lastname,
        city: userProfile?.address?.city,
        state: userProfile?.address?.state,
        zip: userProfile?.address?.zip,
        dob: userProfile?.personal?.dob,
        gender: userProfile?.personal?.gender,
        race: userProfile?.personal?.race,
        email: user.email ?? '',
        $email: user.email ?? '',
        contact: user?.contact?.phone ?? '',
      },
      $token: MIXPANEL_TOKEN,
    });
  }
});

// Pre-save hook
// eslint-disable-next-line func-names
schema.pre('save', function (next) {
  // Ensure the email field exists and is a string
  if (this.email && typeof this.email === 'string') {
    // Lowercase the email field
    this.email = this.email.toLowerCase();
  }
  if (this.username && typeof this.username === 'string') {
    // Lowercase the email field
    this.username = this.username.toLowerCase();
  }
  next();
});

// eslint-disable-next-line func-names
schema.pre('updateOne', async function () {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  const document: any = this.getUpdate();
  if (document && document.username && typeof document.username === 'string') {
    // Lowercase the email field
    document.username = document.username.toLowerCase();
  }
  if (document && document.email && typeof document.email === 'string') {
    // Lowercase the email field in the conditions
    document.email = document.email.toLowerCase();
  }
});

// Pre-fetch hook
// eslint-disable-next-line func-names
schema.pre<{ _conditions: { email: string } }>('findOne', function (next) {
  // Ensure the conditions contain the email field
  if (this._conditions.email && typeof this._conditions.email === 'string') {
    // Lowercase the email field in the conditions
    this._conditions.email = this._conditions.email.toLowerCase();
  }
  next();
});

// Pre-find hook
// eslint-disable-next-line func-names
schema.pre<{ _conditions: { email: string } }>('find', function (next) {
  // Ensure the conditions contain the email field
  if (this._conditions.email && typeof this._conditions.email === 'string') {
    // Lowercase the email field in the conditions
    this._conditions.email = this._conditions.email.toLowerCase();
  }
  next();
});

const UserAccountModel = mongoose.model('user-account', schema);

export default UserAccountModel;

/** USER PROFILE */

const userProfileSchema = new Schema<UserProfile>({
  user: { required: true, type: Schema.Types.ObjectId, ref: UserAccountModel },
  address: new Schema<ProfileAddress>({
    city: String,
    state: String,
    zip: String,
  }),
  personal: new Schema<UserProfilePersonalDetails>({
    dob: Date,
    gender: String,
    race: String,
  }),
});

export const UserProfileModel = mongoose.model('user-profile', userProfileSchema);
