import mongoose, { Schema } from 'mongoose';
import { Service, ServiceCategory, ServiceStatusEnum } from '../../../shared/types/service';

const categorySchema = new Schema<ServiceCategory>({
  description: String,
  logo: String,
  title: { required: true, type: String },
});

export const ServiceCategoryModel = mongoose.model('service-category', categorySchema);

const serviceSchema = new Schema<Service>({
  category: { required: true, type: [Schema.Types.ObjectId], ref: ServiceCategoryModel },
  description: { required: true, type: String },
  email: String,
  logo: { required: true, type: String },
  organization: { required: true, type: String },
  phone: String,
  // serviceId: String,
  states: [String],
  topics: [String],
  title: { required: true, type: String },
  status: { type: String, enum: ServiceStatusEnum, default: ServiceStatusEnum.ACTIVE },
  sequence: { type: Number },
  website: String,
});

export const ServiceModel = mongoose.model('service', serviceSchema);

// find service by ID

// service by state
// find service by category
