import { z } from "zod";
import { zNumber, zString } from ".";

export const topicListValidator = z.object({
  topicIds: z.array(z.string()).optional(),
  query: z.string().optional(),
});

export const createTopicValidator = z.object({
  name: zString,
  tags: z.array(zString),
  questions: z.array(
    z.object({ question: zString, options: z.array(zString) })
  ),
});

export const getUserTopicsValidator = z.object({
  topicId: z.string().optional(),
});

export const upsertUserTopicValidator = z.object({
  topic: zString,
  isCarePartner: z.boolean().optional(),
  isHealthCare: z.boolean().optional(),
  userExperince: z.string().optional(),
  questionAnswers: z.array(
    z.object({
      question: z.string(),
      selectedOption: z.array(z.string()),
      answerText: z.string().optional(),
    })
  ),
});

export const getUserDailyTrackerValidator = z.object({
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  questionIds: z.array(z.string()).optional(),
  topicId: z.string().optional(),
});

export const upsertUserDailyTrackerValidator = z.object({
  topic: zString,
  date: z.date(),
  answers: z.array(
    z.object({
      question: z.string(),
      answerText: z.string(),
      aqi: z.number().optional(),
      selectedOption: z.array(z.string()),
      rawOptionData: z
        .object({
          value: z.string(),
          color: z.string().optional(),
          externalId: z.string().optional(),
        })
        .optional(),
    })
  ),
  completed: z.boolean().optional(),
});

export const getBehaviourAnswerDaysValidator = z.object({
  optionId: zString,
  trackingWindow: zNumber,
});

export const getNextQuestionValidator = z.object({
  currentQuestionId: z.string().optional(),
  selectedOptionExIds: z.union([z.array(z.string()), z.string()]).optional(),
  selectedWeekDay: z.date().optional(),
  topicId: z.string().optional(),
  getQuestionCount: z.boolean().optional()
});

export const getCalenderEventsValidator = z.object({
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  startDateOfMonth: z.string().optional(),
  topicId: z.string().optional()
});

export const unsubscribeUserTopicValidator = z.object({
  topic: zString,
});

export const streakValidator = z.object({
  topicId: z.string(),
});
