import * as z from 'zod';
import { zNumber, zString } from '.';
import { BlogStatus, BlogType } from '../types/Blog';

export const createBlogTagValidator = z.object({
  tag: zString,
});

export const updateBlogTagValidator = createBlogTagValidator.merge(
  z.object({
    tagId: zString,
  })
);

export const manageBlogValidator = z.object({
  blogId: z.string().optional(),
  title: zString,
  contentHtml: zString,
  editorData: z.any(),
  author: zString,
  description: zString.optional(),
  timeToReadInMins: zNumber,
  tags: z.array(zString),
  diseaseTags: z.array(zString),
  banner: zString,
  status: z.nativeEnum(BlogStatus),
  publishDate: z.date().optional().or(z.string().optional()),
});

export const getBlogsValidator = z
  .object({
    tags: z.array(zString).optional(),
    diseaseTags: z.array(zString).optional(),
    status: z.nativeEnum(BlogStatus).optional(),
    author: zString.optional(),
    type: z.nativeEnum(BlogType).optional(),
    publishDate: z.date().optional().or(z.string().optional()),
  })
  .optional();

export const getBlogValidator = z.object({ blog: zString });

export const manageBlogsDashboardValidator = z.object({
  bannerImage: zString,
  title: zString,
});
