{"name": "rabble", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "dev": "NODE_ENV=development npx expo start", "prod": "NODE_ENV=production npx expo start", "update:dev": "NODE_ENV=development eas update", "update:prod": "NODE_ENV=production eas update", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "format": "prettier --write .", "lint": "eslint --fix --max-warnings=3 .", "precommit": "yarn format && yarn lint"}, "dependencies": {"@expo-google-fonts/montserrat": "^0.2.3", "@expo/vector-icons": "^14.0.0", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^3.1.1", "@likashefqet/react-native-image-zoom": "^4.3.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@react-native-masked-view/masked-view": "0.3.2", "@react-native/virtualized-lists": "0.76.3", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/material-top-tabs": "^6.6.6", "@react-navigation/native": "^6.1.6", "@react-navigation/stack": "^6.3.16", "@tanstack/react-query": "^4.0.0", "@trpc/client": "^10.44.1", "@trpc/react": "^9.27.4", "@trpc/react-query": "^10.44.1", "@uiw/react-markdown-preview": "^5.1.1", "axios": "^1.4.0", "clsx": "^1.2.1", "date-fns": "^2.30.0", "expo": "^52.0.11", "expo-application": "~6.0.1", "expo-blur": "~14.0.1", "expo-constants": "~17.0.3", "expo-contacts": "^14.0.5", "expo-device": "~7.0.1", "expo-file-system": "^18.0.11", "expo-font": "~13.0.1", "expo-image-picker": "~16.0.3", "expo-linear-gradient": "~14.0.1", "expo-linking": "~7.0.3", "expo-location": "^18.0.6", "expo-print": "^14.0.3", "expo-sharing": "^13.0.1", "expo-splash-screen": "~0.29.13", "expo-status-bar": "~2.0.0", "expo-tracking-transparency": "~5.1.0", "expo-updates": "~0.27.4", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lottie-react-native": "7.1.0", "lucide-react-native": "^0.321.0", "nativewind": "^2.0.11", "onesignal-expo-plugin": "^2.0.2", "rabble_be": "*", "react": "18.3.1", "react-hook-form": "^7.45.0", "react-native": "0.76.9", "react-native-autocomplete-dropdown": "^4.0.0-rc.5", "react-native-calendars": "^1.1310.0", "react-native-confetti-cannon": "^1.5.2", "react-native-expo-image-cache": "^4.1.0", "react-native-fbsdk-next": "^13.3.0", "react-native-gesture-handler": "~2.20.2", "react-native-markdown-package": "^1.8.2", "react-native-onesignal": "^5.0.5", "react-native-pager-view": "6.5.1", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-shimmer-placeholder": "^2.0.9", "react-native-svg": "15.8.0", "react-native-tab-view": "^3.5.2", "react-native-toast-message": "^2.1.6", "react-native-uuid": "^2.0.3", "react-native-webview": "13.12.5", "react-query": "^3.39.3", "sentry-expo": "~7.0.0", "superjson": "1.13.3", "uuid": "^3.0.0", "yup": "^1.2.0", "zod": "^3.22.4"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/lodash": "^4.14.196", "@types/react": "~18.3.12", "babel-plugin-module-resolver": "^4.1.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-config-universe": "^11.2.0", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^2.8.8", "tailwindcss": "3.3.2", "typescript": "~5.3.3"}, "private": true}