import { <PERSON><PERSON><PERSON>, Modal, View } from "react-native";
import BlogListItem from "./BlogListItem";
import { Blog, BlogStatus } from "../../../../shared/types/Blog";
import Pressable from "@components/common/Pressable";
import FilterIcon from "@assets/svg/services/FilterIcon";
import { useMemo, useState, useEffect } from "react";
import AppButton from "@components/common/AppButton";
import { trpc } from "@providers/RootProvider";
import _ from "lodash";
import AppText from "@components/common/AppText";
import AppCheckBox from "@components/common/AppCheckbox";

type Props = {
  data: Blog[] | undefined;
  setFilterTags?: (v: string[]) => void;
  filterTags?: string[];
  userSelectedTopic?: string;
};

export default function BlogList(props: Props) {
  const [showModal, setShowModal] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  const manageTopics = trpc.manage.getTopics.useQuery({
    ...(props?.userSelectedTopic
      ? { topicIds: [props.userSelectedTopic] }
      : {}),
  });

  trpc.manage.getUserTopics.useQuery(
    {},
    {
      initialData: [],
      onSuccess(data) {
        if (!data.length) return;

        const filterData = data?.map((userTopic) => userTopic?.topic.name);
        props?.setFilterTags?.(filterData);
      },
    }
  );

  const blogs = trpc.blog.getBlogs.useQuery({
    status: BlogStatus.PUBLISHED,
  });

  const tags = useMemo(() => {
    const topicTags = _.flatMap(manageTopics?.data, (topic) => topic.tags);
    const blogTags = _.flatMap(blogs?.data, (blog) =>
      _.map(blog.tags, (tag) => tag.tag)
    );
    return _.uniq([
      ...topicTags,
      ...(props?.userSelectedTopic ? [] : blogTags),
    ]);
  }, [manageTopics]);

  useEffect(() => {
    if (props?.filterTags) {
      setSelectedTags(props?.filterTags);
    }
  }, []);

  console.log("data", selectedTags);
  return (
    <View>
      <View className="mt-1">
        {/* Title and Filter Icon */}
        <View className="flex flex-row justify-between items-center">
          <AppText className="text-lg font-bold">
            {props?.filterTags?.join(", ")}
          </AppText>
          <Pressable onPress={() => setShowModal(true)}>
            <FilterIcon width={24} height={24} />
          </Pressable>
        </View>

        {/* Divider Below */}
        <View
          className="h-[1px] bg-[#AFAFAF] mt-2"
          style={{ marginRight: 50 }}
        />
      </View>
      <Modal
        visible={showModal}
        onRequestClose={() => setShowModal(false)}
        animationType="fade"
        transparent
      >
        <View className="flex-1  bg-black/30">
          <Pressable className="flex-1" onPress={() => setShowModal(false)} />
          <View className=" bg-[#f5f7f9] p-4 rounded-t-xl">
            <View className="bg-[#b0c7da] w-10 h-1 self-center" />

            {/* Filter */}
            <View className="my-4">
              <AppText className="text-lg font-bold mb-2">Select Tags</AppText>
              <FlatList
                data={tags}
                keyExtractor={(item) => item}
                renderItem={({ item }) => (
                  <Pressable
                    onPress={() => {
                      setSelectedTags((prev) =>
                        prev.includes(item)
                          ? prev.filter((tag) => tag !== item)
                          : [...prev, item]
                      );
                    }}
                  >
                    <View className="flex-row mb-4">
                      <AppCheckBox
                        active={selectedTags.includes(item)}
                        pointerEvents="none"
                      />
                      <View className="ml-2">
                        <AppText className="text-base">{item}</AppText>
                      </View>
                    </View>
                  </Pressable>
                )}
              />
            </View>

            <View className="flex-row justify-between my-4 mb-8">
              <AppButton
                variant="outline"
                className="rounded-lg px-4 py-2 m-[4px]"
                onPress={() => {
                  setSelectedTags([]);
                  props?.setFilterTags?.([]);
                  setShowModal(false);
                }}
              >
                Reset
              </AppButton>
              <AppButton
                className="rounded-lg px-4 py-2 m-[4px]"
                onPress={() => {
                  // setFilter(selectedFilter);
                  props?.setFilterTags?.(selectedTags);
                  setShowModal(false);
                }}
              >
                Apply Filter
              </AppButton>
            </View>
          </View>
        </View>
      </Modal>
      {props.data?.map((blog, index) => (
        <BlogListItem key={blog._id?.toString() || index} blog={blog} />
        // <FlatList
        //     data={props.data}
        //     renderItem={({ item }) => (
        //         <BlogListItem blog={item} />
        //     )}
        //     keyExtractor={(item, index) => item._id ? item._id.toString() : index.toString()}
        // />
      ))}
    </View>
  );
}
