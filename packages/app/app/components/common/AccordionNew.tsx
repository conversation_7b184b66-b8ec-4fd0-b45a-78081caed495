import React, { useState } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import Animated from "react-native-reanimated";
import { FontAwesome } from "@expo/vector-icons"; // Icon library
import Pressable from "./Pressable";
import _ from "lodash";

interface AccordionProps {
  sections?: {
    title?: string;
    icon?: JSX.Element;
    items?: { _id?: string; label?: string; note?: string }[];
  }[];
  setActiveItem: (v: any) => void;
  activeItem: string | null;
}

const Accordion: React.FC<AccordionProps> = ({
  sections,
  setActiveItem,
  activeItem,
}) => {
  const [expandedIndex, setExpandedIndex] = useState<number | null>(null);

  const toggleSection = (index: number) => {
    setExpandedIndex((current) => (current === index ? null : index));
  };

  return (
    <View className="">
      {sections?.map((section, index) => (
        <Animated.View
          key={index}
          // @ts-expect-error
          className={`bg-white overflow-hidden ${
            !_.isNull(expandedIndex) ? "" : ""
          }`}
        >
          <TouchableOpacity
            onPress={() => {
              toggleSection(index);
              setActiveItem(null);
            }}
            className={`flex-row rounded-lg justify-between items-center p-3 border border-blue-300 mb-4  border-b-[5px] border-b-[#B4DDFF] ${
              expandedIndex === index ? "bg-[#E1F1FF]" : ""
            }`}
          >
            <View className="flex-row items-center space-x-2">
              {section.icon}
              <View className="flex flex-col">
                <Text className="text-[#004987] text-[18px] font-medium font-montserrat [font-feature-settings:'liga'_off,'clig'_off]">
                  {section.title}
                </Text>
                {!section?.items && (
                  <Text className="text-[#004987] text-[#13px]">
                    (coming soon!)
                  </Text>
                )}
              </View>
            </View>
            {/* <FontAwesome
              name={expandedIndex === index ? "minus" : "plus"}
              size={14}
              color="#5E97C4"
            /> */}
            {section?.items && (
              <Text className="text-[#004987] text-[22px]">
                {expandedIndex === index ? "-" : "+"}
              </Text>
            )}
          </TouchableOpacity>
          {expandedIndex === index && (
            <Animated.View
              //   entering={Animated.FadeIn}
              //   exiting={Animated.FadeOut}
              //   @ts-expect-error
              className=" ml-8 space-y-2"
            >
              {section?.items?.map((item, idx) => (
                <Pressable
                  key={idx}
                  className={`p-2 rounded-md border  border-b-[5px] border-b-[#B4DDFF] ${
                    activeItem === item._id ? "bg-[#E1F1FF]" : ""
                  } border-blue-200 min-h-[50px]
                  ${(section?.items?.length ?? 0) - 1 === idx ? "mb-2" : ""}
                  `}
                  onPress={() => setActiveItem(item._id)}
                >
                  <Text className="text-[#004987] text-[18px]">
                    {item.label}
                  </Text>
                  {item.note && (
                    <Text className="text-[#004987] text-[13px]">
                      {item.note}
                    </Text>
                  )}
                </Pressable>
              ))}
            </Animated.View>
          )}
        </Animated.View>
      ))}
    </View>
  );
};

export default Accordion;
