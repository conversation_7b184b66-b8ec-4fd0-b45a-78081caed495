import clsx from 'clsx';
import React, { Dispatch, SetStateAction, useState } from 'react';
import { ScrollView, View } from 'react-native';
import AppText from './AppText';
import Pressable from './Pressable';
interface Props {
  tabs: (string | JSX.Element | null)[];
  tabState: [number, Dispatch<SetStateAction<number>>];
}
const Tabs = ({ tabs, tabState: [selectedTab, setSelectedTab] }: Props) => {
  return (
    <View>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{
          flexDirection: 'row',
          alignItems: 'center',
          gap: 12,
          paddingTop: 12,
        }}
      >
        {tabs.filter(Boolean).map((tab, idx) => {
          return (
            <Pressable
              actionTag='tabs'
              className={clsx(
                'rounded-full border-neutral-300 border',
                selectedTab === idx && 'border-[#ffcc94] bg-[#fff5e5]',
                typeof tab === 'object' && 'p-1'
              )}
              key={idx}
              onPress={() => {
                setSelectedTab(idx);
              }}
            >
              {typeof tab === 'string' ? (
                <AppText className={clsx('px-4 py-1')}>{tab}</AppText>
              ) : (
                tab
              )}
            </Pressable>
          );
        })}
      </ScrollView>
    </View>
  );
};

export default Tabs;
