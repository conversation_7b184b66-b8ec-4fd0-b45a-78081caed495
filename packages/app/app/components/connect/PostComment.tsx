import { format, parseISO } from 'date-fns';
import LogoSimple from '@assets/svg/LogoOutline';
import ThreeDots from '@assets/svg/connect/ThreeDots';
import { Comment } from '@models/Posts';
import { Image, View } from 'react-native';
import AppText from '@components/common/AppText';
import Like from '@assets/svg/connect/Like';
import Pressable from '@components/common/Pressable';

interface PostCommentProps extends Comment {
  commentOptionsCallback: (comment: Comment) => void;
  onlikeComment: (post: Partial<Comment>, status: boolean) => void;
}

export function PostComment({
  commentOptionsCallback,
  ...otherProps
}: PostCommentProps) {
  const { _id, user, createdAt, comment, isLiked, onlikeComment } = otherProps;
  const handleOpenCommentOptions = () => {
    commentOptionsCallback(otherProps);
  };

  const handleComment = () => onlikeComment(otherProps, isLiked);

  return (
    <>
      <View className='flex flex-row mb-4'>
        {user?.profilePicture ? (
          <Image
            className='w-8 h-8 rounded-full'
            source={{ uri: user?.profilePicture }}
          />
        ) : (
          <LogoSimple width={32} height={32} />
        )}

        <View className='flex flex-1 bg-[#f7f8f9] ml-3 rounded-lg p-3'>
          <View className='flex-row justify-between'>
            <AppText className='font-montserratMedium'>
              {user?.username || user?.firstname}
            </AppText>
            <Pressable
              className='items-end justify-end mr-4 mb-2 z-1'
              onPress={handleOpenCommentOptions}
              actionTag='comment options'
            >
              <ThreeDots />
            </Pressable>
          </View>
          <AppText className='text-[10px] font-montserratMedium text-neutral-500'>
            {format(new Date(createdAt), 'MMM dd yyyy, hh:mm a')}
          </AppText>

          {/* Comment */}
          <AppText>{comment}</AppText>
          <Pressable
            actionTag='like comment'
            onPress={handleComment}
            hitSlop={10}
            className='mt-2'
          >
            <Like active={isLiked} />
          </Pressable>
        </View>
      </View>
    </>
  );
}
