import AppText from "@components/common/AppText";
import Pressable from "@components/common/Pressable";
import clsx from "clsx";
import _ from "lodash";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { Dimensions, Platform, View } from "react-native";
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";
import { Option, QuestionTypes, Topic } from "../../../../shared/types/manage";
import WarningIcon from "../../assets/svg/WarningIcon";
import ManageTextParser from "./ManageTextParser";
import AppButton from "@components/common/AppButton";
import { MID } from "rabble_be/src/@types/common";
import LoadingScreen from "@components/common/LoadingScreen";
import { InfoIcon } from "lucide-react-native";
import { useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { ManageNavParams } from "@navigation/manage-navigator/ManageNavParams";
// import ProgressBar from "@components/common/ProgressBar";

const SCREEN_WIDTH = Dimensions.get("screen").width;
const COMPONENT_WIDTH = SCREEN_WIDTH - 16 * 2;

function ManageDailyQuestion({
  dailyTrackerQuestion,
  onValueSelect,
  topic,
  selectedOptions,
  onAnimationComplete,
  loading,
  progressQuestion,
  userSelectedTopic,
  isOnboardingCheckIn,
}: {
  dailyTrackerQuestion: Topic["dailyTrackerQuestions"][number];
  topic: string;
  userSelectedTopic?: any;
  onValueSelect: (
    data: {
      dtq: Topic["dailyTrackerQuestions"][number];
      selectedOptions: Option[];
    },
    scrollToNext?: boolean
  ) => void;
  onAnimationComplete?: () => void;
  selectedOptions?: [MID];
  loading?: boolean;
  progressQuestion: number;
  isOnboardingCheckIn?: boolean;
}) {
  // const allOptionsSelected: Option[] = [];
  const navigation = useNavigation<StackNavigationProp<ManageNavParams>>();
  const [allOptionsSelected, setAllOptionsSelected] = useState<Option[]>([]);
  const { isEmergencyType, isHorizontalSelect } = useMemo(
    () => ({
      isEmergencyType: dailyTrackerQuestion?.type === QuestionTypes.Emergency,
      isHorizontalSelect:
        dailyTrackerQuestion?.type === QuestionTypes.HorizontalSelect,
    }),
    [dailyTrackerQuestion?.type]
  );

  const widthAnim = useSharedValue(0);

  const isFirstRender = useRef(true);
  // Dont animate if the option is already selected
  useEffect(() => {
    if (isFirstRender.current) {
      widthAnim.value = COMPONENT_WIDTH - 16 * 2;
      isFirstRender.current = false;
    }
  }, [selectedOptions]);

  useEffect(() => {
    if (selectedOptions && dailyTrackerQuestion.options) {
      const userSelectedOptions = _.filter(
        dailyTrackerQuestion.options,
        (option) => _.includes(selectedOptions, option._id)
      );
      setAllOptionsSelected(userSelectedOptions);
    }
  }, []);

  const animatedStyle = useAnimatedStyle(
    () => ({ width: widthAnim.value }),
    []
  );

  const expandItem = (isMultiSelect: boolean) => {
    widthAnim.value = 0;
    widthAnim.value = withTiming(
      COMPONENT_WIDTH - 16 * 2,
      { duration: 2000 },
      () => {
        if (onAnimationComplete && !isMultiSelect)
          runOnJS(onAnimationComplete)();
      }
    );
  };

  return (
    <View
      style={{
        width: SCREEN_WIDTH,
        alignItems: "center",
      }}
    >
      <View
        className={`bg-white rounded-2xl p-4 shadow-md flex flex-col shadow-primary/10 ${
          loading ? "h-[300px]" : ""
        }`}
        // style={{ width: COMPONENT_WIDTH }}
        style={
          Platform.OS !== "ios"
            ? {
                shadowColor: "#000000",
                shadowOffset: { width: 10, height: 10 },
                shadowOpacity: 6,
                shadowRadius: 6,
                elevation: 8,
                width: COMPONENT_WIDTH,
              }
            : {
                shadowColor: "#000000",
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.2,
                shadowRadius: 6,
                elevation: 8,
                width: COMPONENT_WIDTH,
              }
        }
      >
        {loading ? (
          <LoadingScreen />
        ) : (
          <View>
            {isEmergencyType ? (
              <View className="flex flex-row justify-center items-center mb-[8px]">
                <WarningIcon />
              </View>
            ) : (
              <View>
                <View className="flex justify-center items-center">
                  <AppText className="uppercase text-center text-foundation font-montserratSemiBold color-[#FF8E1C]">
                    {topic}
                  </AppText>
                  <View className="w-[150px] bg-gray-200 h-1.5 rounded-lg overflow-hidden flex-row  mt-[4px]">
                    <View
                      style={{
                        width: `${progressQuestion}%`,
                        backgroundColor: "#85b9e7",
                      }}
                    />
                  </View>
                  {isOnboardingCheckIn && topic !== "Breast Cancer" && (
                    <Pressable
                      onPress={() =>
                        navigation.navigate("ManageLearningScreen", {
                          topicId: String(userSelectedTopic),
                          source: 'daily'
                        })
                      }
                      className="absolute top-0 right-0"
                    >
                      <InfoIcon size={14} color="#FF8E1C" />
                    </Pressable>
                  )}
                </View>
              </View>
            )}

            <View>
              <ManageTextParser color="#004987" fontSize={28} boldFontSize={28}>
                {dailyTrackerQuestion.question}
              </ManageTextParser>
            </View>

            {/* hiding the sequence as we don't have logic for it */}
            {/* <AppText className='uppercase text-center text-foundation mt-2'>
        {questionIndex + 1}
      </AppText> */}
            <View className="flex flex-row justify-between items-center mb-[8px] mt-[8px]">
              <View>
                <AppText className="text-[14px] font-montserratRegular text-[#00284A] px-[24px] text-center mb-[8px]">
                  {dailyTrackerQuestion.type === QuestionTypes.MultiSelect
                    ? "(Select all that apply)"
                    : dailyTrackerQuestion?.subText}
                </AppText>
              </View>
              {dailyTrackerQuestion.type === QuestionTypes.MultiSelect && (
                <View className="flex justify-center items-center mb-[8px]">
                  <AppButton
                    title="Next"
                    size="xs"
                    onPress={() => {
                      if (
                        QuestionTypes.MultiSelect === dailyTrackerQuestion?.type
                      ) {
                        onValueSelect(
                          {
                            dtq: dailyTrackerQuestion,
                            selectedOptions: allOptionsSelected,
                          },
                          true
                        );
                      }
                    }}
                  />
                </View>
              )}
            </View>
            {/* Question Options */}
            <View
              className={[
                isHorizontalSelect
                  ? "flex flex-row justify-between items-end"
                  : "",
              ].join(" ")}
              style={{ gap: 8 }}
            >
              {dailyTrackerQuestion?.options?.map((o, idx) => {
                return (
                  <Pressable
                    key={idx}
                    className={isHorizontalSelect ? "flex-1" : ""}
                    onPress={() => {
                      // allOptionsSelected.push(o);

                      if (
                        QuestionTypes.MultiSelect !== dailyTrackerQuestion?.type
                      ) {
                        setAllOptionsSelected([o]);
                        onValueSelect(
                          {
                            dtq: dailyTrackerQuestion,
                            selectedOptions: [o],
                          },
                          true
                        );
                      } else {
                        setAllOptionsSelected((prev) => {
                          console.log({ prev, o });
                          if (
                            !prev.find(
                              (p) => p._id?.toString() === o._id?.toString()
                            )
                          ) {
                            return [...prev, o];
                          } else {
                            return prev.filter(
                              (p) => p._id?.toString() !== o._id?.toString()
                            );
                          }
                        });
                      }
                      // onValueSelect({
                      //   dtq: dailyTrackerQuestion,
                      //   selectedOptions: [o],
                      // });
                      expandItem(
                        QuestionTypes.MultiSelect === dailyTrackerQuestion?.type
                      );
                    }}
                  >
                    <View
                      className={[
                        `w-full rounded-xl relative flex justify-between align-center p-3 border border-[#B4DDFF] border-b-[5px] border-b-[#B4DDFF]`,
                        _.includes(_.map(allOptionsSelected, "_id"), o._id)
                          ? "border-2 border-primary"
                          : "",
                        isEmergencyType && idx === 1 ? "bg-[#004987]" : "",
                      ].join(" ")}
                    >
                      {/**Animate Here */}
                      {(_.includes(selectedOptions, o._id) ||
                        _.includes(
                          _.map(allOptionsSelected, "_id"),
                          o._id
                        )) && (
                        <Animated.View
                          className={clsx(
                            "absolute w-[100%] rounded-xl bg-red  top-0 left-0 right-0 bottom-0 opacity-10",
                            dailyTrackerQuestion.type !==
                              QuestionTypes.MultiSelect && "bg-[#004987]"
                          )}
                          style={
                            dailyTrackerQuestion.type !==
                            QuestionTypes.MultiSelect
                              ? animatedStyle
                              : { width: "100%" }
                          }
                        />
                      )}
                      <AppText
                        style={
                          isEmergencyType ? {} : { color: o.color || "#004987" }
                        }
                        className={[
                          "text-center text-base font-montserratSemiBold ",
                          isEmergencyType
                            ? (idx === 0 && "text-[#E88119]") ||
                              (idx === 1 && "text-white")
                            : "",
                        ].join(" ")}
                      >
                        {o.value}
                      </AppText>
                    </View>
                  </Pressable>
                );
              })}
            </View>
          </View>
        )}
      </View>
    </View>
  );
}

export default React.memo(ManageDailyQuestion);
