import ButterFlyIcon from "@assets/svg/ButterFlyIcon";
import QuestionPrompt from "@assets/svg/QuestionPrompt";
import ProgressBar from "@components/common/ProgressBar";
import Screen from "@components/common/Screen";
import { ChevronLeft } from "lucide-react-native";
import React, { useState, useMemo, useEffect } from "react";
import { Dimensions, View } from "react-native";
import AppButton from "@components/common/AppButton";
import BookIcon from "@assets/svg/BookIcon";
import CompleteDailyCheckInIcon from "@assets/svg/CompleteDailyCheckInIcon";
import TeamIcon from "assets/svg/TeamIcon";
import { useNavigation } from "@react-navigation/native";
import { RootNavigationProp } from "@navigation/root-navigator";
import Pressable from "@components/common/Pressable";
import withPressAnimation from "@components/common/AnimateButton";
import usePersistedUser from "@hooks/persistUser";
import { trpc } from "@providers/RootProvider";
import _ from "lodash";
import { UserExperience } from "../../../../shared/types/manage";
import AppText from "@components/common/AppText";
import RecommendedIcon from "@assets/svg/RecomendedIcon";
import LoadingScreen from "@components/common/LoadingScreen";
import useScreenTracking from "@hooks/useScreenTracking";
import mixpanel from "@utils/mixpanel";
import CreateRabblePopup from "./CreateRabbleCard";
import { alert } from "@providers/AlertProvider";

const AnimatedAppButton = withPressAnimation(AppButton);

const screenWidth = Dimensions.get("window").width;
const cardWidth = screenWidth * 0.9;

export default function OnboardingLastQuestion() {
  const navigation = useNavigation<RootNavigationProp>();
  const [selectedOption, setSelectedOption] = useState<number | null>(null);
  const { setPersistedUser, user } = usePersistedUser();
  const { setOnboardingProgressScreen } = useScreenTracking();
  const { data: userData } = user;
  const { mutateAsync: saveUserAsync } = setPersistedUser;
  const { mutateAsync: saveProgressScreen } = setOnboardingProgressScreen;
  const [loading, setLoading] = useState(false);
  const [isOpenRabbleCard, setRabbleCardOpen] = useState(false);

  const userTopics = trpc.manage.getUserTopics.useQuery({});
  const updateUserPartial = trpc.user.updateUserPartial.useMutation();

  const userTopicName = useMemo(
    () => _.head(userTopics?.data)?.topic?.name,
    [userTopics?.data]
  );

  const checkInOptions = useMemo(
    () => [
      {
        icon: <CompleteDailyCheckInIcon />,
        title: "Complete a daily check-in",
        description:
          userTopicName === "Asthma"
            ? "Take 5 seconds to enter your asthma daily check-in"
            : `Take 5 seconds to enter your ${userTopicName} daily check-in`, // Dynamic description
        isRecommended: true,
      },
      {
        icon: <BookIcon />,
        title: "Start my learning",
        description:
          userTopicName === "Asthma"
            ? "Take the first lesson of the Asthma learning module"
            : `Take the first lesson of the ${userTopicName} learning module`,
        isRecommended: false,
        redirect: "OnboardingLearningQuestion",
      },
      {
        icon: <TeamIcon />,
        title: "Invite my support team",
        description: "Create a digital community through invites",
        isRecommended: false,
        redirect: "InviteTeam",
      },
    ],
    [userTopicName]
  );

  const userExperince = useMemo(
    () => _.get(_.head(userTopics?.data), "userExperince"),
    [userTopics?.data]
  );

  useEffect(() => {
    if (user?.data) {
      (async () => {
        await mixpanel.trackEvent(
          "Start screen view",
          {
            email_or_phone:
              user?.data?.email || user?.data?.contact?.phone || "",
          },
          user?.data?._id?.toString(),
          "v2"
        );
      })();
    }
  }, [user?.data]);

  useEffect(() => {
    saveProgressScreen("OnboardingLastQuestion");
    const sub = setTimeout(() => {
      setLoading(false);
    }, 1000);
    return () => clearTimeout(sub);
  }, []);

  const recommendedIndex = userExperince === UserExperience.BEGINNER ? 0 : 0;
  const handleNext = async () => {
    // mixpanel.trackEvent(
    //   "ONBOARDING_ACTION",
    //   {
    //     action: "Startup view selected (Step 15) (Account Created)",
    //     email: userData?.email || "",
    //   },
    //   "",
    //   "v2"
    // );
    if (!selectedOption && _.head(userTopics?.data)?.topic?.name === "Breast") {
      return;
    }
    if (user?.data) {
      await mixpanel.trackEvent(
        "Continue clicked (Step 2)(Account Setup)",
        {
          email_or_phone: user?.data?.email || user?.data?.contact?.phone || "",
        },
        user?.data?._id?.toString(),
        "v2"
      );
    }
    if (!selectedOption) {
      navigation.navigate("OnboardingDailyCheckIn");
    } else if (selectedOption === 2) {
      // navigation.navigate("OnboardingWalkThroughRabbleScreen");
      setRabbleCardOpen(true);
      // navigation.navigate("CreateNewRabble")
    } else {
      navigation.navigate("OnboardingDailyLearning");
    }
  };
  const disableLearningOption =
    (_.head(userTopics?.data)?.topic?.name === "Breast Cancer" &&
      selectedOption === 1) ||
    selectedOption === null;
  if (loading) return <LoadingScreen />;

  return (
    <Screen>
      <View className="flex-row items-center mt-8">
        <ChevronLeft
          color={"#004987"}
          size={22}
          className="ml-4"
          onPress={() => navigation.goBack()}
        />
        <ProgressBar
          total={7}
          current={6}
          style={{ marginLeft: 8, flex: 1, marginRight: 16 }}
        />
      </View>
      <View className="flex-1 items-center mt-[-12px]">
        <View className="flex-row items-center mb-8">
          <ButterFlyIcon height={140} width={90} />
          <QuestionPrompt message="Where would you like to start?" />
        </View>

        <View className="flex-1 p-2 mt-[-36px]" style={{ width: cardWidth }}>
          {checkInOptions.map((option, index) => (
            <Pressable
              key={index}
              className={`relative flex-row items-center bg-white p-4 mb-4 rounded-lg border border-[#B4DDFF] overflow-hidden ${
                selectedOption === index ? "bg-[#E1F1FF]" : ""
              }  border-b-[5px] border-b-[#B4DDFF]`}
              onPress={async () => {
                // TODO: Fix this
                if (index === 2) return;
                setSelectedOption(index);
                if (user?.data) {
                  await mixpanel.trackEvent(
                    "Starting option selected (Step 1)(Account Setup)",
                    {
                      email_or_phone:
                        user?.data?.email || user?.data?.contact?.phone || "",
                      option_text: checkInOptions[index]?.title,
                    },
                    user?.data?._id?.toString(),
                    "v2"
                  );
                }
              }}
            >
              {index === recommendedIndex && (
                <View className="absolute right-0 top-0">
                  <RecommendedIcon />
                </View>
              )}
              {/* )} */}
              {option.icon}
              <View className="ml-4 flex-1">
                <AppText className="text-base font-montserratSemiBold text-[#004987] font-montserrat mt-4">
                  {option.title}
                </AppText>
                <AppText className="text-sm font-montserratMedium text-[#004987] font-montserrat">
                  {option.description}
                  {(_.head(userTopics?.data)?.topic?.name === "Breast Cancer" &&
                    option.title === "Start my learning") ||
                  option.title === "Invite my support team"
                    ? ` (coming soon!)`
                    : ""}
                </AppText>
              </View>
            </Pressable>
          ))}
        </View>
      </View>
      <View className="flex flex-row items-center justify-between mx-4 mb-4">
        <AnimatedAppButton
          btnContainer="flex flex-row"
          textClassName="text-[21px]"
          title="CONTINUE"
          variant={disableLearningOption ? "disabled" : "new-primary"}
          className="flex-1 rounded-full"
          onPress={handleNext}
          disabled={disableLearningOption}
          style={
            disableLearningOption
              ? {}
              : {
                  shadowColor: "#003366",
                  shadowOffset: { width: 0, height: 3 },
                  shadowOpacity: 1,
                  shadowRadius: 1,
                  elevation: 5,
                }
          }
        />
      </View>

      <CreateRabblePopup
        visible={isOpenRabbleCard}
        onClose={() => setRabbleCardOpen(false)}
      />
    </Screen>
  );
}
