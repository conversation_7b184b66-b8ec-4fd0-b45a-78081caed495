import ButterFlyIcon from "@assets/svg/ButterFlyIcon";
import AppButton from "@components/common/AppButton";
import Screen from "@components/common/Screen";
import { RootNavigationProp } from "@navigation/root-navigator";
import { useNavigation } from "@react-navigation/native";
import { ChevronLeft } from "lucide-react-native";
import { View } from "react-native";
import React, { useEffect, useState } from "react";
import EllipseIcon from "@assets/svg/EllipseIcon";
import OnboardingW2 from "@assets/svg/OnboardingW2";
import withPressAnimation from "@components/common/AnimateButton";
import usePersistedUser, { useSession } from "@hooks/persistUser";
import { useAppProvider } from "@providers/AppProvider";
import { trpc } from "@providers/RootProvider";
import useScreenTracking from "@hooks/useScreenTracking";
import OnboardingLoader from "./OnboardingGuruLoader";

const AnimatedAppButton = withPressAnimation(AppButton);

export default function OnboardingWelcomeUserScreen() {
  const navigation = useNavigation<RootNavigationProp>();
  const { setOnboardingProgressScreen } = useScreenTracking();
  const { persistedUser, setPersistedUser } = usePersistedUser();
  const { user } = useSession();

  const loginUser = async () => {
    await setOnboardingProgressScreen.mutateAsync("");
    // @ts-expect-error
    await setPersistedUser.mutateAsync(user);
  };

  const handleNext = async () => {
    // navigation.navigate("OnboardingDailyCheckIn");
    await loginUser();
  };

  return (
    <Screen className="flex-1">
      <View className="flex-1">
        <View className="flex-1 justify-center item-center">
          <View className="ml-24">
            <OnboardingW2
              message={`Your new Rabble has been successfully created!  Let’s create the first post.`}
              xMargin={8}
              yMargin={20}
              dx={8}
              dxMarginPercent="7.5%"
            />
          </View>
          <View className="flex mb-12 justify-center item-center">
            <ButterFlyIcon
              className="self-center mt-[-16px] min-h-[250px]"
              style={{ transform: [{ scale: 0.68 }] }}
            />
            <View className="self-center mt-[-32px] mr-8">
              <EllipseIcon />
            </View>
          </View>
        </View>
      </View>

      <View className="flex flex-row items-center justify-between mx-4 mb-2">
        <AnimatedAppButton
          btnContainer="flex flex-row p-1"
          title="CONTINUE"
          variant="new-primary"
          className="flex-1 rounded-full"
          textClassName="text-[21px]"
          style={{
            shadowColor: "#003366",
            shadowOffset: { width: 0, height: 3 },
            shadowOpacity: 1,
            shadowRadius: 1,
            elevation: 5,
          }}
          onPress={handleNext}
        />
      </View>
    </Screen>
  );
}
