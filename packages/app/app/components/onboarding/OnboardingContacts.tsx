import ButterFlyIcon from "@assets/svg/ButterFlyIcon";
import QuestionPrompt from "@assets/svg/QuestionPrompt";
import ProgressBar from "@components/common/ProgressBar";
import Screen from "@components/common/Screen";
import { ChevronLeft } from "lucide-react-native";
import React, { useState, useMemo, useEffect } from "react";
import { Dimensions, Text, View } from "react-native";
import AppButton from "@components/common/AppButton";
import BookIcon from "@assets/svg/BookIcon";
import CompleteDailyCheckInIcon from "@assets/svg/CompleteDailyCheckInIcon";
import { useNavigation } from "@react-navigation/native";
import { RootNavigationProp } from "@navigation/root-navigator";
import Pressable from "@components/common/Pressable";
import withPressAnimation from "@components/common/AnimateButton";
import usePersistedUser from "@hooks/persistUser";
import { trpc } from "@providers/RootProvider";
import _ from "lodash";
import { UserExperience } from "../../../../shared/types/manage";
import AppText from "@components/common/AppText";
import RecommendedIcon from "@assets/svg/RecomendedIcon";
import LoadingScreen from "@components/common/LoadingScreen";
import ContactIcon from "@assets/svg/ContactIcon";
import * as Contacts from "expo-contacts";
import { Linking } from "react-native";
import HandIcon from "@assets/svg/HandIcon";

import Svg, {
  Path,
  Defs,
  Pattern,
  Use,
  Image,
  SvgProps,
} from "react-native-svg";
import AppScrollView from "@components/common/AppScrollView";

function ContactHandIcon(props: SvgProps) {
  return (
    <Svg
      width={81}
      height={81}
      viewBox="0 0 81 81"
      fill="none"
      // xmlns="http://www.w3.org/2000/svg"
      // xmlnsXlink="http://www.w3.org/1999/xlink"
      {...props}
    >
      <Path fill="url(#pattern0_10483_20472)" d="M0 0H81V81H0z" />
      <Defs>
        <Pattern
          id="pattern0_10483_20472"
          patternContentUnits="objectBoundingBox"
          width={1}
          height={1}
        >
          <Use xlinkHref="#image0_10483_20472" transform="scale(.01235)" />
        </Pattern>
        <Image
          id="image0_10483_20472"
          width={81}
          height={81}
          preserveAspectRatio="none"
          xlinkHref="data:image/png;base64,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"
        />
      </Defs>
    </Svg>
  );
}

const openAppSettings = () => {
  Linking.openSettings();
};

const countryDialingCodes: Record<string, string> = {
  IN: "91",
  US: "1",
  UK: "44",
  CA: "1",
  AU: "61",
  // Add more as needed
};

const AnimatedAppButton = withPressAnimation(AppButton);

const screenWidth = Dimensions.get("window").width;
const cardWidth = screenWidth * 0.9;

export default function OnboardingContacts() {
  const navigation = useNavigation<RootNavigationProp>();
  const [enable, setEnable] = useState(false);
  const [formattedContacts, setFormattedContacts] = useState<
    { id: string; name: string; phoneNumbers: string; email: string }[]
  >([]);

  useEffect(() => {
    (async () => {
      // openAppSettings();
      const { status } = await Contacts.requestPermissionsAsync();

      console.log({ status });
      if (status === "granted") {
        const { data } = await Contacts.getContactsAsync({
          fields: [Contacts.Fields.Emails, Contacts.Fields.PhoneNumbers],
        });
        // console.log({ status, data });
        if (data.length > 0) {
          const formattedContacts = data.map((contact) => ({
            id: contact.id || "",
            name: contact.name,
            email: contact.emails?.[0].email,
            phoneNumbers:
              contact.phoneNumbers?.map((phone) => {
                const dialingCode =
                  countryDialingCodes[phone?.countryCode?.toUpperCase()] || "";
                return dialingCode
                  ? `+${dialingCode}${phone?.digits}`
                  : phone?.digits;
              })[0] || "",
          }));
          const sortedContacts = _.orderBy(
            formattedContacts,
            ["name"],
            ["asc"]
          );
          setFormattedContacts(sortedContacts);
          // console.log("Formatted Contacts:", formattedContacts);
        }
      }
      setEnable(true);
    })();
  }, []);

  //   if (loading) return <LoadingScreen />;

  return (
    <Screen>
      <AppScrollView>
      <View className="flex-1 items-center mt-[-12px]">
        <View className="flex-row items-center mt-8">
          <ChevronLeft
            color={"#004987"}
            size={22}
            className="ml-4"
            onPress={() => navigation.goBack()}
          />
          <ProgressBar
            total={3}
            current={1}
            style={{ marginLeft: 8, flex: 1, marginRight: 16 }}
          />
        </View>
        <View className="flex-row items-center mt-8">
          <ButterFlyIcon height={140} width={90} />
          <QuestionPrompt message="Let’s invite members  to your Rabble!" />
        </View>

        <View className="bg-[#E1F1FF] text-center p-2 h-[91px] w-[94px] rounded-xl items-center">
          <ContactHandIcon />
        </View>
        <Text className="mb-8 mt-2 text-center text-[#336D9F] text-center font-[Montserrat] text-[12px] font-bold leading-[130%]">
          Member
        </Text>

        {/* contact card  */}
        <View className="flex-1 items-center justify-center p-4 mb-16">
          {/* Contact Access Card */}
          <View className="bg-white rounded-lg p-4 shadow-lg border border-[#B4DDFF] border-b-[5px]">
            {/* Icon */}
            <View className="flex items-center mb-4 pt-16">
              <ContactIcon />
              <View className="flex -mt-8 -mr-20">
                <HandIcon />
              </View>
            </View>

            {/* Title */}
            <Text className="pb-16 mt-8 text-[#004987] text-center font-montserrat text-[24px] font-semibold leading-normal text-center">
              "myRabble" would like to access your contacts
            </Text>

            {/* Subtitle */}
            {/* <Text className="text-[#004987] text-center font-montserrat text-[18px] font-normal leading-normal text-center mt-2">
              Build your team on myRabble
            </Text> */}

            {/* Buttons */}
            {/* <View className="flex flex-row justify-between mt-6">
              <Pressable
                className="flex-1 mr-2 p-2 rounded-lg"
                onPress={() => {
                  navigation.navigate("OnboardingWelcomeUserScreen");
                }}
              >
                <Text className="text-center text-[#004987] text-center font-montserrat text-[18px] font-normal leading-normal">
                  Don't Allow
                </Text>
              </Pressable>

              <Pressable
                className="flex-1 mr-2 p-2 rounded-lg"
                onPress={() => setEnable(true)}
              >
                <Text className="text-center text-[#0B79D3] text-center font-montserrat text-[18px] font-semibold leading-normal">
                  Continue
                </Text>
              </Pressable>
            </View> */}
          </View>
        </View>
      </View>
      </AppScrollView>
      <View className="flex flex-row items-center justify-between mx-4 mb-4">
        {/* <View className="flex flex-row p-1 mb-2"> */}
        <AnimatedAppButton
          btnContainer="flex flex-row"
          textClassName="text-[21px]"
          title="CONTINUE"
          className="flex-1 rounded-full"
          variant={!enable ? "disabled" : "new-primary"}
          style={
            enable
              ? {
                  shadowColor: "#003366",
                  shadowOffset: { width: 0, height: 3 },
                  shadowOpacity: 1,
                  shadowRadius: 1,
                  elevation: 5,
                }
              : {}
          }
          disabled={!enable}
          onPress={() =>
            navigation.navigate("OnboardingContactList", {
              contacts: formattedContacts,
            })
          }
        />
        {/* </View> */}
      </View>
    </Screen>
  );
}
