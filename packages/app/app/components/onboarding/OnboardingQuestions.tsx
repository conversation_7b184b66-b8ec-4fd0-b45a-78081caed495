import React, { useState, useMemo, useEffect } from "react";
import { ScrollView, View } from "react-native";
import Screen from "@components/common/Screen";
import AppButton from "@components/common/AppButton";
import { ChevronLeft } from "lucide-react-native";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { RootNavigationProp } from "@navigation/root-navigator";
import Pressable from "@components/common/Pressable";
import AppText from "@components/common/AppText";
import clsx from "clsx";
import { Dimensions } from "react-native";
import QuestionPrompt from "@assets/svg/QuestionPrompt";
import ButterFlyIcon from "@assets/svg/ButterFlyIcon";
import { trpc } from "@providers/RootProvider";
import ProgressBar from "@components/common/ProgressBar";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import OnboardingBuilding from "./OnboardingBuilding";
import _ from "lodash";
import { QuestionTypes, UserExperience } from "../../../../shared/types/manage";
import withPressAnimation from "@components/common/AnimateButton";
import AsyncStorage from "@react-native-async-storage/async-storage";
import mixpanel from "@utils/mixpanel";
import { useSession } from "@hooks/persistUser";
import useScreenTracking from "@hooks/useScreenTracking";

const AnimatedAppButton = withPressAnimation(AppButton);

const screenWidth = Dimensions.get("window").width;
const cardWidth = screenWidth * 0.9;

const carePartnerAnswerIds = ["672b917533d5cc5cc4a73360"];
const healthProviderText = "I am a Healthcare Professional";
const breastHealthProviderText = "I am a Healthcare Provider";
const beginnerAnswers = ["new to asthma", "basic knowledge"];

export default function OnboardingQuestion() {
  const navigation = useNavigation<RootNavigationProp>();
  const {
    topicId,
    currentIndex: paramIndex,
    questionAnswer: paramAnswers,
    fromManageScreen,
  } = useRoute<RouteProp<RootNavParams, "OnboardingQuestion">>()?.params;

  console.log({ fromManageScreen });
  const [currentIndex, setCurrentIndex] = useState(0);
  const [selectedOption, setSelectedOption] = useState<any[]>([]);
  const [questionAnswer, setQuestionAnswer] = useState<any[]>([]);
  const [currentQuestionAnswerText, serCurrentQuestionAnswerText] =
    useState("");
  const [loading, setLoading] = useState(false);
  const [isBuildingScreen, setIsBuildingScreen] = useState(true);
  const [optionPrompt, setOptionPrompt] = useState("");
  const { setOnboardingProgressScreen } = useScreenTracking();
  const { mutateAsync: saveProgressScreen } = setOnboardingProgressScreen;

  const { user } = useSession();

  const topics = trpc.manage.getTopics.useQuery({
    ...(topicId ? { topicIds: [topicId] } : {}),
  });
  const subscribeTopic = trpc.manage.upsertUserTopic.useMutation();

  const questions = useMemo(() => {
    return topics?.data?.[0]?.questions || [];
  }, [topics?.data]);

  useEffect(() => {
    setTimeout(() => {
      setIsBuildingScreen(false);
    }, 2500);
  }, []);

  useEffect(() => {
    if (paramIndex !== undefined && paramAnswers) {
      setCurrentIndex(paramIndex);
      setQuestionAnswer(paramAnswers);

      const lastAnswer = paramAnswers.find(
        (q) => q.question === questions[paramIndex]?._id
      );
      setSelectedOption(lastAnswer?.selectedOption || []);
      setOptionPrompt(lastAnswer?.answerText || "");
    }
  }, [paramIndex, paramAnswers, questions]);

  const handleContinue = async () => {
    setLoading(true);
    const selectAnswerIds = _.flattenDeep(
      _.map(questionAnswer, "selectedOption")
    );
    const userExp = _.some(questionAnswer, (answer) =>
      _.includes(beginnerAnswers, answer.answerText)
    )
      ? UserExperience.BEGINNER
      : UserExperience.ADVANCED;
    const res = await subscribeTopic.mutateAsync({
      // @ts-expect-error
      topic: topics?.data?.[0]?._id,
      userExperince: userExp,
      isHealthCare:
        _.last(questionAnswer)?.answerText === healthProviderText ||
        _.last(questionAnswer)?.answerText === breastHealthProviderText,
      isCarePartner: _.some(selectAnswerIds, (id) =>
        _.includes(carePartnerAnswerIds, id)
      ),
      questionAnswers: questionAnswer,
    });
    console.log({ res, currentQuestionAnswerText });
    if (
      currentQuestionAnswerText === healthProviderText ||
      currentQuestionAnswerText === breastHealthProviderText
    ) {
      mixpanel.trackEvent(
        "Connection explained (Step 8)(Account Creation)",
        {
          email_or_phone: user?.email || user?.contact?.phone || "",
          explaination_option: _.last(questionAnswer)?.answerText,
        },
        user?._id?.toString(),
        "v2"
      );
      setLoading(false);
      navigation.navigate("OnboardingProvider", { fromManageScreen });
      return;
    }
    setCurrentIndex(currentIndex + 1);
    setSelectedOption([]);
    setOptionPrompt("");
    setLoading(false);
    if (currentIndex + 1 === questions.length) {
      if (fromManageScreen) {
        saveProgressScreen("");
        AsyncStorage.setItem("selectedTopic", topicId);
        navigation.replace("ManageHomeScreen");
        return;
      }
      AsyncStorage.setItem("selectedTopic", topicId);
      mixpanel.trackEvent(
        "Connection explained (Step 8)(Account Creation)",
        {
          email_or_phone: user?.email || user?.contact?.phone || "",
          explaination_option: _.last(questionAnswer)?.answerText,
        },
        user?._id?.toString(),
        "v2"
      );
      navigation.navigate("OnboardingLastQuestion", {
        topicId,
        currentIndex,
        questionAnswer,
      });
    }
  };

  const currentQuestion = questions[currentIndex];

  if (isBuildingScreen) {
    return <OnboardingBuilding />;
  }

  return (
    <Screen className="flex-1">
      <View className="flex-row items-center mt-8">
        <ChevronLeft
          color={"#004987"}
          size={22}
          className="ml-4"
          onPress={() => {
            if (currentIndex > 0) {
              setCurrentIndex(currentIndex - 1);
              const previousQuestion = questionAnswer.find(
                (q) => q.question === questions[currentIndex - 1]?._id
              );
              const question = questions[currentIndex - 1];
              const option = _.find(
                question?.options,
                (option: any) =>
                  option?._id.toString() ===
                  previousQuestion?.selectedOption.toString()
              );
              setSelectedOption(previousQuestion?.selectedOption || []);
              setOptionPrompt(option?.prompt || question?.question);
            } else {
              navigation.goBack();
            }
          }}
        />
        <ProgressBar
          total={questions?.length + 2}
          current={currentIndex + 2}
          style={{ marginLeft: 8, flex: 1, marginRight: 16 }}
        />
      </View>
      <ScrollView>
        <View className="flex-1 items-center">
          <View className="flex-row items-center gap-x-2">
            <ButterFlyIcon height={140} width={90} />
            <QuestionPrompt
              dx={10}
              bubbleWidth={250}
              message={optionPrompt || currentQuestion?.question}
            />
          </View>

          {currentQuestion?.options?.map((option, idx) => {
            return (
              <Pressable
                key={idx}
                className={clsx("mr-12")}
                style={{
                  width: cardWidth * 0.9,
                  elevation: 5,
                  shadowColor: "#000",
                  shadowOffset: { width: 0, height: 4 },
                  shadowOpacity: 0.2,
                  shadowRadius: 5,
                }}
                onPress={() => {
                  setLoading(false);
                  serCurrentQuestionAnswerText(option?.value);
                  if (currentQuestion?.type === QuestionTypes.MultiSelect) {
                    setSelectedOption((prev) => {
                      if (prev.includes(option?.value)) {
                        return prev.filter((id) => id !== option?.value);
                      } else {
                        return [...prev, option?.value];
                      }
                    });
                  } else {
                    setSelectedOption([option?.value]);
                  }
                  if (option.prompt) {
                    setOptionPrompt(option.prompt);
                  }

                  const existingAnswer = questionAnswer.find(
                    (q) => q.question === currentQuestion?._id
                  );

                  if (!_.isEmpty(existingAnswer)) {
                    if (currentQuestion?.type === QuestionTypes.MultiSelect) {
                      const isSelected = existingAnswer.selectedOption.includes(
                        option?._id
                      );
                      let updatedSelectedOptions = isSelected
                        ? existingAnswer.selectedOption.filter(
                            (id) => id !== option?._id
                          ) // Deselect
                        : [...existingAnswer.selectedOption, option?._id];

                      setQuestionAnswer((prev) =>
                        prev.map((q) =>
                          q.question === currentQuestion?._id
                            ? {
                                ...q,
                                selectedOption: updatedSelectedOptions,
                                answerText: option.value,
                              }
                            : q
                        )
                      );
                    } else {
                      setQuestionAnswer((prev) =>
                        prev.map((q) =>
                          q.question === currentQuestion?._id
                            ? {
                                ...q,
                                selectedOption: [option?._id],
                                answerText: option.value,
                              }
                            : q
                        )
                      );
                    }
                  } else {
                    if (currentQuestion?.type === "multiSelect") {
                      setQuestionAnswer((prev) => [
                        ...prev,
                        {
                          question: currentQuestion?._id,
                          selectedOption: [option?._id],
                          answerText: option.value,
                        },
                      ]);
                    } else {
                      setQuestionAnswer((prev) => [
                        ...prev,
                        {
                          question: currentQuestion?._id,
                          selectedOption: [option?._id],
                          answerText: option.value,
                        },
                      ]);
                    }
                  }
                }}
              >
                <View
                  className={`p-3 rounded-[5px] border-[1px] m-2 border-[#B4DDFF] border-b-[5px] border-b-[#B4DDFF] ${
                    _.includes(selectedOption, option?.value) ||
                    _.includes(selectedOption, option?._id.toString())
                      ? "bg-[#E1F1FF]"
                      : "bg-white"
                  }`}
                  style={{
                    width: cardWidth * 0.95,
                  }}
                >
                  <AppText className="text-[18px] text-left  text-[#004987]">
                    {option?.value}
                  </AppText>
                </View>
              </Pressable>
            );
          })}
        </View>
      </ScrollView>

      <View className="flex flex-row items-center justify-between mx-4 mb-6">
        <AnimatedAppButton
          btnContainer="flex flex-row p-1 mb-2"
          title="CONTINUE"
          variant={
            _.isEmpty(selectedOption) || loading ? "disabled" : "new-primary"
          }
          className="flex-1 rounded-full"
          textClassName="text-[21px]"
          style={
            !_.isEmpty(selectedOption) && !loading
              ? {
                  shadowColor: "#003366",
                  shadowOffset: { width: 0, height: 3 },
                  shadowOpacity: 1,
                  shadowRadius: 1,
                  elevation: 5,
                }
              : {}
          }
          onPress={handleContinue}
          disabled={!selectedOption || loading}
        />
      </View>
    </Screen>
  );
}
