import AsyncStorage from "@react-native-async-storage/async-storage";
import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  type ReactNode,
} from "react";

import uuid from "react-native-uuid";

const APP_INSTANCE_ID_KEY = "APP_INSTANCE_ID";

type AppInstanceIdContextType = {
  instanceId: string | null;
  loading: boolean;
  error: Error | null;
};

const AppInstanceIdContext = createContext<
  AppInstanceIdContextType | undefined
>(undefined);

// ✅ Reusable function: Create or Read App Instance ID
export const createOrReadAppInstanceId = async (): Promise<string> => {
  const storedId = await AsyncStorage.getItem(APP_INSTANCE_ID_KEY);

  if (storedId) {
    return storedId;
  } else {
    const newId = uuid.v4() as string;
    await AsyncStorage.setItem(APP_INSTANCE_ID_KEY, newId);
    return newId;
  }
};

export const AppInstanceIdProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  const [instanceId, setInstanceId] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const initializeInstanceId = async () => {
      try {
        const id = await createOrReadAppInstanceId();
        setInstanceId(id);
      } catch (err) {
        console.error("Failed to initialize app instance ID:", err);
        setError(err as Error);
      } finally {
        setLoading(false);
      }
    };

    initializeInstanceId();
  }, []);

  return (
    <AppInstanceIdContext.Provider value={{ instanceId, loading, error }}>
      {children}
    </AppInstanceIdContext.Provider>
  );
};

export const useAppInstanceId = () => {
  const context = useContext(AppInstanceIdContext);

  if (context === undefined) {
    throw new Error(
      "useAppInstanceId must be used within an AppInstanceIdProvider"
    );
  }

  return context;
};
