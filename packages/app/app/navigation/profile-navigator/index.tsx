import NavigationHeader from "@components/navigation/NavigationHeader";
import { Entypo } from "@expo/vector-icons";
import {
  createStackNavigator,
  StackNavigationOptions,
} from "@react-navigation/stack";
import Profile from "@screens/profile";
import UserProfileEditScreen from "@screens/profile/UserProfileScreen";

import { ProfileNavParams } from "./ProfileNavParams";
import ContactUs from "@screens/profile/ContactUs";
import Policies from "@screens/profile/Policies";
import CreateProfile from "@screens/profile/CreateProfile";
import FindPatient from "@screens/profile/FindPatient";
import RelationshipWithPatientScreen from "@screens/caregiver/RelationshipWithPatientScreen";
import CreatePatient from "@screens/profile/CreatePatient";
import Caregiver from "@screens/caregiver";
import CreateCaregiverPatient from "@screens/caregiver/CreateCaregiverPatient";
import FindCaregiverPatient from "@screens/caregiver/FindCaregiverPatient";
import HealthProfileCaregiver from "@screens/caregiver/HealthProfileCaregiver";
import PatientUserAndHealthProfile from "@screens/profile/PatientUserAndHealthProfile";

const options: StackNavigationOptions = {
  header: NavigationHeader,
  headerLeft: () => (
    <Entypo name="chevron-small-left" size={24} color="black" />
  ),
  headerShown: false,
  cardStyle: { backgroundColor: "#fff" },
};

const Stack = createStackNavigator<ProfileNavParams>();

export default function ProfileNavigator() {
  return (
    <Stack.Navigator
      screenOptions={{ ...options }}
      initialRouteName="ProfileScreen"
    >
      <Stack.Screen name="ProfileScreen" component={Profile} />

      <Stack.Screen
        name="CreateProfileScreen"
        component={CreateProfile}
        options={{
          headerShown: true,
          headerTitle: "",
        }}
      />
      <Stack.Screen
        name="HealthUserEditScreen"
        component={UserProfileEditScreen}
        options={{ headerShown: true, headerTitle: "profile" }}
      />
      <Stack.Screen
        name="FindPatientScreen"
        component={FindPatient}
        options={{ headerShown: true, headerTitle: "" }}
      />
      <Stack.Screen
        name="RelationshipWithPatientScreen"
        component={RelationshipWithPatientScreen}
        options={{ headerShown: true, headerTitle: "" }}
      />
      <Stack.Screen
        name="CreatePatientScreen"
        component={CreatePatient}
        options={{ headerShown: true, headerTitle: "" }}
      />
      <Stack.Screen
        name="ContactScreen"
        component={ContactUs}
        options={{ headerShown: true, headerTitle: "contact us" }}
      />
      <Stack.Screen
        name="PolicyScreen"
        component={Policies}
        options={{ headerShown: true, headerTitle: "policies" }}
      />
      <Stack.Screen
        name="Caregiver"
        component={Caregiver}
        options={{ headerShown: true, headerTitle: "", headerLeft: () => null }}
      />
      <Stack.Screen
        name="CreateCaregiverPatient"
        component={CreateCaregiverPatient}
        options={{ headerShown: true, headerTitle: "" }}
      />
      <Stack.Screen
        name="FindCaregiverPatient"
        component={FindCaregiverPatient}
        options={{ headerShown: true, headerTitle: "" }}
      />
      <Stack.Screen
        name="HealthProfileCaregiver"
        component={HealthProfileCaregiver}
        options={{ headerShown: true, headerTitle: "" }}
      />
      <Stack.Screen
        name="PatientUserHealthProfile"
        component={PatientUserAndHealthProfile}
        options={{ headerShown: true, headerTitle: "patient details" }}
      />
    </Stack.Navigator>
  );
}
