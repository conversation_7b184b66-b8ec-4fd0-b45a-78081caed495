import {
  FlatList,
  View,
  StyleSheet,
  Image,
  RefreshControl,
} from "react-native";
import ConnectPost, { Post } from "../../components/connect/Post";
import Screen from "@components/common/Screen";
import { ImgSelect } from "@assets/svg/connect/ImgSelect";
import AppText from "@components/common/AppText";
import { memo, useCallback, useMemo, useState, useEffect } from "react";
import LogoSimple from "@assets/svg/LogoOutline";
import InfoModal from "@components/common/InfoModal";
import Trash from "@assets/svg/connect/Trash";
import Report from "@assets/svg/connect/Report";
import ModalBottomSheet from "@components/common/ModalBottomSheet";
import SectionLink from "@components/profile/ProfileSectionItem";
import { PostTypes } from "@models/Posts";
import { StackNavigationProp } from "@react-navigation/stack";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import useAuthGuard from "@hooks/useAuthGuard";
import { trpc } from "@providers/RootProvider";
import { useSession } from "@hooks/persistUser";
import errorHandler from "@utils/errorhandler";
import Toast from "react-native-toast-message";
import LoadingScreen from "@components/common/LoadingScreen";
import _ from "lodash";
import Pressable from "@components/common/Pressable";
import EditIcon from "@assets/svg/profile/EditIcon";
import mixpanel from "@utils/mixpanel";

interface ConnectHeaderProps {
  handleCreatePost: () => void;
  profileImageUrl?: string;
}

function ConnectScreenHeader({
  handleCreatePost,
  profileImageUrl,
}: ConnectHeaderProps) {
  return (
    <>
      {/* <ConnectLogo
        className="my-4 self-center"
        style={{ transform: [{ scale: 1.0 }] }}
      /> */}
      {/* Header */}
      <Pressable
        actionTag="create post"
        style={styles.createAPostButton}
        className="flex flex-row items-center justify-between bg-neutral-100 p-2 rounded-full z-10 mt-4"
        onPress={handleCreatePost}
      >
        <View className="flex flex-row items-center gap-2">
          {profileImageUrl ? (
            <Image
              source={{ uri: profileImageUrl }}
              className="h-10 w-10 rounded-full"
            />
          ) : (
            <LogoSimple width={40} height={40} />
          )}
          <AppText className="text-neutral-400">
            Share something here...
          </AppText>
        </View>
        <ImgSelect className="mr-2" />
      </Pressable>
    </>
  );
}

interface Props {
  rabbleGroupId?: string;
  connectHeader?: JSX.Element;
  showCreatePost?: boolean;
  postType?: PostTypes;
  showPosts?: boolean;
}

export default memo(function Connect({
  rabbleGroupId,
  connectHeader,
  showCreatePost = true,
  postType = "CONNECT",
  showPosts = true,
}: Props) {
  const rootNavigation = useNavigation<StackNavigationProp<RootNavParams>>();
  const { user } = useSession();
  const isCaregiver = useMemo(() => user?.isCaregiver, [user]);

  const authGuard = useAuthGuard();
  const utils = trpc.useUtils();

  const getUserById = trpc.user.getUserById.useMutation();

  const handleCreatePost = () => {
    authGuard(() => {
      if (postType)
        rootNavigation.navigate("CreatePostScreen", {
          postType,
          rabbleGroup: rabbleGroupId,
        });
    });
  };

  const handleEditPost = () => {
    // console.log({ selectedPost });
    setReportBottomSheetStatus(false);
    authGuard(() => {
      if (postType)
        rootNavigation.navigate("CreatePostScreen", {
          postType,
          rabbleGroup: rabbleGroupId,
          connectPost: selectedPost,
        });
    });
  };

  // If is connect screen show posts from other groups else show group specific posts
  const createdGroups = trpc.rabbleGroups.getUserCreatedGroups.useQuery();
  const joinedGroups = trpc.rabbleGroups.userJoinedGroups.useQuery();
  const patientGroups =
    trpc.rabbleGroups.caregiverPatientJoinedGroups.useQuery();

  const rabbelGroupIds = useMemo(() => {
    const groupIds = [
      ...(createdGroups.data || [])?.map((_) => _._id.toString()),
      ...(joinedGroups.data || [])?.map((_) => _.rabbleGroup._id.toString()),
      ...(patientGroups.data || [])?.map((_) => _.rabbleGroup._id.toString()),
    ];

    return groupIds.length ? groupIds : undefined;
  }, [createdGroups.data, joinedGroups.data]);

  const {
    data: posts,
    fetchNextPage,
    isLoading: postsLoading,
    refetch: refetchPosts,
  } = trpc.connect.getPosts.useInfiniteQuery(
    {
      limit: 30,
      user: user?._id as string,
      postType,
      rabbleGroup: rabbleGroupId ?? rabbelGroupIds,
      mergeGroupPosts: !rabbleGroupId,
    },
    {
      initialCursor: 0,
      getNextPageParam: (lastPage) => lastPage.pagination.nextCursor,
    }
  );

  useEffect(() => {
    (async () => {
      await mixpanel.trackEvent(
        "Rabbles screen view",
        {
          email: user?.email || "",
          phone: user?.contact?.phone || "",
        },
        String(user?._id),
        "v2"
      );
    })();
  }, []);

  useFocusEffect(
    useCallback(() => {
      refetchPosts();
    }, [])
  );

  const flatPosts = (posts?.pages || []).map((_) => _.posts).flat();

  const handleNextPage = () => fetchNextPage();

  const groupMembers = trpc.rabbleGroups.getUsersInGroup.useQuery(
    { group: rabbelGroupIds },
    { enabled: !!rabbelGroupIds?.length }
  );

  const groupUserByGroupId = useMemo(
    () => _.groupBy(groupMembers.data, "rabbleGroup"),
    [groupMembers.data]
  );

  const handlePostDetailPageNav = useCallback(
    (post: Partial<Post>) => {
      if (postType)
        authGuard(() =>
          rootNavigation.navigate("PostDetailsScreen", {
            postId: post._id!,
            postType,
            groupId: post.rabbleGroup?.at(0)?._id,
            // @ts-ignore
            groupRole: groupUserByGroupId[post.rabbleGroup?.at(0)?._id]?.find(
              (gpu) => String(gpu?.user?._id) === String(user?._id)
            )?.role,
          })
        );
    },
    [rootNavigation, authGuard]
  );

  const likePost = trpc.connect.likePost.useMutation({
    onMutate: async ({ status, post: postId }) => {
      await utils.connect.getPosts.cancel();

      utils.connect.getPosts.setInfiniteData(
        // @ts-ignore
        {
          limit: 30,
          user: user?._id as string,
          postType,
          rabbleGroup: rabbleGroupId ?? rabbelGroupIds,
          mergeGroupPosts: !rabbleGroupId,
        },
        (data) => {
          if (!data) {
            return {
              pages: [],
              pageParams: [],
            };
          }

          return {
            ...data,
            pages: data.pages.map((page) => ({
              ...page,
              posts: page.posts.map((post) => {
                if (post._id === postId) return { ...post, isLiked: status };
                return post;
              }),
            })),
          };
        }
      );
    },
  });

  const handleLike = useCallback(
    (post: Partial<Post>, status: boolean, groupName?: string) => {
      authGuard(async () => {
        try {
          await likePost.mutateAsync(
            {
              status: !status,
              post: post._id as string,
            },
            {
              onSuccess: () => refetchPosts(),
            }
          );
          // const groupUser = post?.rabbleGroup[0]?._id  && await getUserById.mutateAsync(
          //   String(post?.rabbleGroup[0]?.createdBy) || ""
          // );
          // await mixpanel.trackEvent(
          //   "Post liked (Rabble screen)",
          //   {
          //     email: user?.email || "",
          //     post_username: post?.user[0]?.username || "",
          //     post_id: post?._id || "",
          //     group_title: groupName || "",
          //     group_id: post?.rabbleGroup[0]?._id || "",
          //     group_status: !groupName
          //     ? "User is not part of any group"
          //     : `User is part of ${groupName || ""}`,
          //     group_admin: `${groupUser?.firstname || ""} ${groupUser?.firstname || ""}`,
          //   },
          //   String(user?._id),
          //   "v2"
          // );
        } catch (ex) {
          errorHandler(ex);
        }
      });
    },
    []
  );

  // utility states
  const [reportBottomSheetStatus, setReportBottomSheetStatus] = useState(false);
  const [reportModalVisible, setReportModalVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);

  console.log({ deleteModalVisible });

  const [selectedPost, setSelectedPost] = useState<Partial<Post> | undefined>();

  // reporting posts
  const handleOptionsSelect = useCallback(
    (post: Partial<Post>) => {
      authGuard(() => {
        setSelectedPost(post);
        setReportBottomSheetStatus(true);
      });
    },
    [authGuard]
  );

  const deleteAction = () => {
    setDeleteModalVisible(true);
    // setReportBottomSheetStatus(false);
  };

  const reportAction = () => {
    setReportModalVisible(true);
    // setReportBottomSheetStatus(false);
  };

  const _deletePost = trpc.connect.deletePost.useMutation();

  const _reportPost = trpc.connect.reportPost.useMutation();

  const deletePost = async () => {
    try {
      await _deletePost.mutate(selectedPost?._id!, {
        onSettled: () => {
          refetchPosts();
          setDeleteModalVisible(false);
          setReportBottomSheetStatus(false);
        },
      });

      // handle toast
      Toast.show({
        type: "success",
        text1: "post deleted successfully",
        visibilityTime: 2000,
      });
    } catch (ex) {
      errorHandler(ex);
    }
  };

  const reportPost = async () => {
    await _reportPost.mutate(selectedPost?._id!);
    // handle toast
    Toast.show({
      type: "success",
      text1: "post reported successfully",
      visibilityTime: 2000,
    });
    setReportModalVisible(false);
    setReportBottomSheetStatus(false);
  };

  if (postsLoading || joinedGroups.isLoading || createdGroups.isLoading)
    return <LoadingScreen />;

  return (
    <Screen className="flex flex-1">
      <InfoModal
        visible={deleteModalVisible}
        label="delete post"
        description="Are you sure you want to delete this post? This action cannot be undone."
        onRequestClose={setDeleteModalVisible}
        actionText="Yes"
        showLogo
        handleAction={deletePost}
        secondaryText="No"
      />
      {/* Modal for Report*/}
      <InfoModal
        visible={reportModalVisible}
        label="report post"
        description="Are you sure you want to report this post?"
        showLogo={true}
        onRequestClose={setReportModalVisible}
        actionText="Yes"
        handleAction={reportPost}
        secondaryText="No"
      />
      <FlatList
        contentContainerStyle={{
          paddingHorizontal: 16,
        }}
        showsVerticalScrollIndicator={false}
        decelerationRate="normal"
        data={showPosts ? flatPosts : []}
        onEndReached={showPosts ? handleNextPage : undefined}
        onEndReachedThreshold={0.8}
        keyExtractor={(posts) => posts._id.toString()}
        refreshControl={
          <RefreshControl refreshing={postsLoading} onRefresh={refetchPosts} />
        }
        ListHeaderComponent={() => (
          <>
            {connectHeader}
            {showCreatePost && (
              <ConnectScreenHeader
                handleCreatePost={handleCreatePost}
                profileImageUrl={user?.profilePicture}
              />
            )}
          </>
        )}
        renderItem={({ item }) => {
          return (
            <ConnectPost
              onPress={handlePostDetailPageNav}
              onLike={handleLike}
              onOptionsSelect={handleOptionsSelect}
              {...item}
            />
          );
        }}
      />
      {/* Post Options Bottomsheet */}
      <ModalBottomSheet
        visible={reportBottomSheetStatus}
        onClose={setReportBottomSheetStatus}
      >
        {/* Content of the modal */}
        <View className="my-6 mx-2">
          <AppText className="font-montserratMedium text-xl my-2">
            user setting
          </AppText>
          <View>
            {(selectedPost?.user?.[0]._id === user?._id ||
              (selectedPost?.rabbleGroup &&
                (groupUserByGroupId[
                  String(selectedPost?.rabbleGroup?.at(0)?._id)
                ]?.find((gpu) => String(gpu?.user?._id) === String(user?._id))
                  ?.role === "admin" ||
                  groupUserByGroupId[
                    String(selectedPost?.rabbleGroup?.at(0)?._id)
                  ]?.find((gpu) => String(gpu?.user?._id) === String(user?._id))
                    ?.role === "owner"))) && (
              <View>
                <SectionLink
                  title="delete post"
                  icon={<Trash />}
                  onPress={deleteAction}
                />
                <SectionLink
                  title="Edit post"
                  className="ml-2"
                  icon={<EditIcon />}
                  onPress={handleEditPost}
                />
              </View>
            )}
            {selectedPost?.user?.[0]._id !== user?._id && (
              <SectionLink
                title="report this post"
                icon={<Report />}
                onPress={reportAction}
              />
            )}
          </View>
        </View>
      </ModalBottomSheet>
      {/* Modal for Delete*/}
    </Screen>
  );
});

const styles = StyleSheet.create({
  createAPostButton: {
    shadowColor: "#004987",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 5,
  },
});
