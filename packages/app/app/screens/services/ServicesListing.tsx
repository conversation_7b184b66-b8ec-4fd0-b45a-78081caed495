import AppText from "@components/common/AppText";
import Pressable from "@components/common/Pressable";
import Screen from "@components/common/Screen";
import Filter from "@components/services/Filter";
import ServiceCard from "@components/services/ServiceCard";
import { ServicesNavParams } from "@navigation/services-navigator/profile-navigator/ServicesNavParams";
import { trpc } from "@providers/RootProvider";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { useEffect, useState, useMemo } from "react";
import { FlatList, ScrollView, View } from "react-native";
import _ from "lodash";
import AppCheckBox from "@components/common/AppCheckbox";
import AppButton from "@components/common/AppButton";
import mixpanel from "@utils/mixpanel";
import { useSession } from "@hooks/persistUser";

export default function ServicesListing() {
  const {
    params: { categoryId, header },
  } = useRoute<RouteProp<ServicesNavParams, "ServicesListingScreen">>();

  const navigation = useNavigation<StackNavigationProp<ServicesNavParams>>();

  const [selectedCategoryName, setSelectedCategoryName] =
    useState<string>(header);
  const [selectedState, setSelectedState] = useState<string>("");

  const [categorySheetVisible, setCategorySheetVisible] =
    useState<boolean>(false);
  const [stateSheetVisible, setStateSheetVisible] = useState<boolean>(false);
  const [topicSheetVisible, setTopicSheetVisible] = useState<boolean>(false);
  const [selectedTopics, setSelectedTopics] = useState<string[]>([]);
  const [activeTopics, setActiveTopics] = useState<string[]>([]);
  const { user } = useSession();

  const createMixPanelEvent = (topics = selectedTopics) => {
    mixpanel.trackEvent(
      "Services filter applied (Services Screen)",
      {
        email: user?.email || "",
        phone: user?.contact?.phone || "",
        filter_name: "Topic filter",
        filter_value: JSON.stringify(topics?.join(',')) || "",
      },
      String(user?._id),
      "v2"
    );
  }

  const applyTopicsFilter = () => {
    createMixPanelEvent();
    setActiveTopics([...selectedTopics]);
    refetchServices();
  };

  // const manageTopics = trpc.manage.getTopics.useQuery({});
  trpc.manage.getUserTopics.useQuery(
    {},
    {
      initialData: [],
      onSuccess(data) {
        if (!data.length) return;
        const filterData = data?.map((userTopic) => userTopic?.topic.name);
        createMixPanelEvent(filterData);
        setSelectedTopics(filterData);
        setActiveTopics([...filterData]);
      },
    }
  );

  const allTopics = trpc.manage.getTopics.useQuery({});

  // console.log({ userTopics: userTopics?.data })
  const topics = useMemo(
    () => _.map(allTopics?.data, (userTopic) => userTopic?.name),
    [allTopics]
  );

  useEffect(() => {
    if (categoryId) {
      navigation.setParams({ header: selectedCategoryName });
      setCategorySheetVisible(false);
      setStateSheetVisible(false);
      setTopicSheetVisible(false);
    }
  }, [selectedCategoryName, categoryId]);

  useEffect(() => {
    (async () => {
      mixpanel.trackEvent(
        "Services category screen view (Services Screen)",
        {
          email: user?.email || "",
          phone: user?.contact?.phone || "",
          category_name: selectedCategoryName || "",
        },
        String(user?._id),
        "v2"
      );
    })();
  }, []);

  const {
    data: serviceListings,
    isLoading: isServiceListingsLoading,
    refetch: refetchServices,
  } = trpc.service.getService.useQuery({
    category: categoryId,
    ...(selectedState && { states: [selectedState, "National"] }),
    ...(activeTopics.length && { topics: activeTopics }),
  });

  const { data: getCategoriesData, isLoading: getCategoriesLoading } =
    trpc.service.getCategories.useQuery();

  const { data: stateFilterOptions, isLoading: isStateFilterOptionsLoading } =
    trpc.lib.states.useQuery();

  const onSelectCategory = (categoryName: string) => {
    mixpanel.trackEvent(
      "Services filter applied(Services Screen)",
      {
        email: user?.email || "",
        phone: user?.contact?.phone || "",
        filter_name: "category filter",
        filter_value: categoryName || "",
      },
      String(user?._id),
      "v2"
    );
    const selectedCategory = getCategoriesData?.find(
      (category) => category.title === categoryName
    );
    if (selectedCategory) {
      setSelectedCategoryName(categoryName);
      setCategorySheetVisible(false);
      navigation.setParams({ categoryId: selectedCategory._id }); // Set the categoryId
    }
  };

  const onSelectState = (stateName: string) => {
    mixpanel.trackEvent(
      "Services filter applied (Services Screen)",
      {
        email: user?.email || "",
        phone: user?.contact?.phone || "",
        filter_name: "state filter",
        filter_value: stateName || "",
      },
      String(user?._id),
      "v2"
    );
    setSelectedState(stateName);
    setStateSheetVisible(false);
  };

  const handleDeleteState = () => {
    setSelectedState("");
    setStateSheetVisible(false);
  };

  if (
    isStateFilterOptionsLoading ||
    isServiceListingsLoading ||
    getCategoriesLoading
  )
    return null;

  return (
    <Screen className="flex flex-1">
      <View style={{ gap: 4 }} className="flex-row items-center">
        <Filter
          name={selectedCategoryName || "Category"}
          isVisible={categorySheetVisible}
          onSheetStatusChange={setCategorySheetVisible}
          state={false}
        >
          <AppText className="text-xl font-montserratBold my-2 pb-5">
            Category
          </AppText>
          <ScrollView
            className="h-[40%] mb-5 gap-5"
            showsVerticalScrollIndicator={false}
          >
            {getCategoriesData?.map((category) => (
              <Pressable
                actionTag="service category"
                key={category?._id.toString()}
                onPress={() => onSelectCategory(category.title)}
              >
                <AppText>{category.title}</AppText>
              </Pressable>
            ))}
          </ScrollView>
        </Filter>
        <Filter
          state={false}
          name={selectedState || "State"}
          isVisible={stateSheetVisible}
          onSheetStatusChange={setStateSheetVisible}
          onDeleteState={handleDeleteState}
        >
          <AppText className="text-xl font-montserratBold my-2 pb-5">
            State
          </AppText>
          <ScrollView
            className="h-[40%] mb-5 gap-5"
            showsVerticalScrollIndicator={false}
          >
            {stateFilterOptions?.map((state) => (
              <Pressable
                key={state}
                actionTag="service state filter"
                onPress={() => onSelectState(state)}
              >
                <AppText>{state}</AppText>
              </Pressable>
            ))}
          </ScrollView>
        </Filter>
        <Filter
          state={false}
          name={
            activeTopics?.length
              ? _.truncate(_.join(activeTopics, ", "), { length: 10 })
              : "Topics"
          }
          isVisible={topicSheetVisible}
          onSheetStatusChange={setTopicSheetVisible}
        >
          <View className="bg-[#b0c7da] w-10 h-1 self-center" />
          <AppText className="text-xl font-montserratBold my-2 pb-5">
            Select Topics
          </AppText>
          <ScrollView
            className="h-[10%] mb-4 gap-5"
            showsVerticalScrollIndicator={false}
          >
            {topics?.map((topic) => (
              <Pressable
                key={topic}
                onPress={() => {
                  setSelectedTopics((prev) =>
                    prev.includes(topic)
                      ? prev.filter((t) => t !== topic)
                      : [...prev, topic]
                  );
                }}
              >
                <View className="flex-row mb-[-4px]">
                  <AppCheckBox
                    active={selectedTopics.includes(topic)}
                    pointerEvents="none"
                  />
                  <View className="ml-2">
                    <AppText className="text-base">{topic}</AppText>
                  </View>
                </View>
              </Pressable>
            ))}
          </ScrollView>
          <View className="flex-row justify-between my-4 mb-8">
            <AppButton
              variant="outline"
              className="rounded-lg px-4 py-2 m-[4px]"
              onPress={() => {
                setSelectedTopics([]);
              }}
            >
              Reset
            </AppButton>
            <AppButton
              className="rounded-lg px-4 py-2 m-[4px]"
              onPress={() => {
                applyTopicsFilter();
                setTopicSheetVisible(false);
              }}
            >
              Apply Filter
            </AppButton>
          </View>
        </Filter>
      </View>
      <>
        {serviceListings?.length ?? 0 > 0 ? (
          <>
            <FlatList
              data={serviceListings || []}
              keyExtractor={(item) => item._id.toString()}
              renderItem={({ item }) => (
                <ServiceCard item={item} categoryName={selectedCategoryName} />
              )} // Render ServiceCard with the item prop
            />
          </>
        ) : (
          <View className="justify-center items-center mt-6">
            <AppText>No Data Found </AppText>
          </View>
        )}
      </>
    </Screen>
  );
}
